# BL0906 电压采样方式条件编译实现总结

## 🎯 **实现完成情况**

根据修改计划，已成功实现BL0906电压采样方式的条件编译功能。

### ✅ **已完成的修改**

#### 1. **Python配置文件修改** (`__init__.py`)
- 添加了 `CONF_VOLTAGE_SAMPLING_MODE` 配置常量
- 定义了 `VOLTAGE_SAMPLING_MODES` 枚举字典
- 在 `BASE_CONFIG_SCHEMA` 中添加了 `voltage_sampling_mode` 选项，默认值为 `"transformer"`
- 在 `to_code()` 函数中实现了条件编译宏的生成逻辑

#### 2. **校准头文件重构** (`bl0906_calibration.h`)
- 完全重构了文件结构，移除了所有重复定义
- 使用条件编译 `#ifdef/#elif/#else` 来区分两种采样方式
- 提供了默认行为：未明确定义时自动使用电压互感器方式
- 实现了编译时错误检查机制

#### 3. **测试配置文件**
- 创建了 `test_voltage_sampling_transformer.yaml` - 电压互感器采样方式测试
- 创建了 `test_voltage_sampling_resistor_divider.yaml` - 电压分压电阻采样方式测试

## 🔧 **技术实现细节**

### **条件编译宏定义**
```cpp
// 默认行为
#ifndef BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
#ifndef BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#define BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#endif
#endif

// 条件编译块
#ifdef BL0906_VOLTAGE_SAMPLING_TRANSFORMER
    // 电压互感器采样参数和计算
#elif defined(BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER)
    // 电压分压电阻采样参数和计算
#else
    #error "必须定义采样方式宏"
#endif
```

### **Python配置解析**
```python
# 配置schema
cv.Optional(CONF_VOLTAGE_SAMPLING_MODE, default="transformer"): cv.enum(VOLTAGE_SAMPLING_MODES, lower=True)

# 代码生成
voltage_sampling_mode = config[CONF_VOLTAGE_SAMPLING_MODE]
if voltage_sampling_mode == "transformer":
    cg.add_define("BL0906_VOLTAGE_SAMPLING_TRANSFORMER")
elif voltage_sampling_mode == "resistor_divider":
    cg.add_define("BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER")
```

## 📊 **两种采样方式对比**

### **电压互感器采样方式（默认）**
- **分压电阻**: `Rf = 100000.0f Ω`
- **采样电阻**: `R46 = 100 Ω`
- **电压系数**: `Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf)`
- **功率系数**: `Kp = 2.3847e-7 * Ki * Kv`

### **电压分压电阻采样方式**
- **上拉电阻**: `Rf = 1500.0f kΩ`
- **下拉电阻**: `Rv = 1.0f kΩ`
- **电压系数**: `Kv = 13162.0f * Rv * 1000 * Gain_V / (Vref * (Rf + Rv))`
- **功率系数**: `Kp = 2.3825e-8 * Kv * Ki`

## 🎯 **使用方法**

### **YAML配置示例**

#### 使用电压互感器采样（默认）
```yaml
bl0906_factory:
  voltage_sampling_mode: transformer  # 或省略此行
  # 其他配置...
```

#### 使用电压分压电阻采样
```yaml
bl0906_factory:
  voltage_sampling_mode: resistor_divider
  # 其他配置...
```

## ⚡ **实现优势**

1. **编译时优化**: 零运行时开销，最高性能
2. **代码简洁**: 消除重复定义，结构清晰
3. **安全可靠**: 编译时错误检查，默认安全配置
4. **用户友好**: 简单的YAML配置，易于使用
5. **向后兼容**: 默认行为保持不变

## 🔍 **验证结果**

- ✅ 条件编译宏正确生成
- ✅ 两种采样方式参数正确定义
- ✅ 校准系数计算公式正确
- ✅ 默认行为工作正常
- ✅ 错误检查机制有效
- ✅ 测试配置文件可用

## 📝 **注意事项**

1. **默认行为**: 未配置时自动使用电压互感器方式
2. **编译时确定**: 采样方式在编译时固定，无法运行时切换
3. **参数差异**: 两种方式使用不同的参数和计算公式
4. **向后兼容**: 不影响现有代码的功能

## 🏁 **总结**

本次实现成功达到了修改计划的所有目标：
- 提供了简洁的YAML配置接口
- 实现了高效的条件编译机制
- 保持了代码的简洁性和可维护性
- 确保了编译时的安全性和错误检查

用户现在可以通过简单的YAML配置来选择合适的电压采样方式，代码将在编译时自动选择对应的参数和计算公式。 