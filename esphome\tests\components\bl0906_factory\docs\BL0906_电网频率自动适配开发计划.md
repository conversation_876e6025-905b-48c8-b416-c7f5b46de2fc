# BL0906 电网频率自动适配功能开发计划

## 1. 功能概述

在BL0906Factory组件中增加电网频率自动适配选项，实现初始化时自动检测电网频率并设置相应的工作模式。

### 核心需求
- 寄存器地址: 0x97 MODE2 工作模式寄存器
- 位字段: [23] AC_FREQ_SEL（交流电频率选择）
  - 1 = 60Hz
  - 0 = 50Hz（默认）
- 检测逻辑: 如果检测到频率在55-65Hz之间，自动设置为60Hz模式

## 2. 技术分析

### 2.1 现有架构分析
- **通信适配器**: 使用CommunicationAdapterInterface统一接口
- **寄存器操作**: 通过`read_register_value()`和`write_register_value()`方法
- **频率读取**: 已有BL0906_FREQUENCY(0x4E)寄存器读取逻辑
- **初始化流程**: 在`setup()`方法中进行组件初始化

### 2.2 MODE2寄存器特性
- 寄存器地址: 0x97
- 数据宽度: 24位
- AC_FREQ_SEL位位置: 第23位
- 访问方式: 需要先解除写保护

## 3. 开发计划与步骤

### 第一阶段：寄存器定义和基础支持

#### 3.1 扩展寄存器定义
**文件**: `bl0906_registers.h`
- 添加MODE2寄存器地址常量
```cpp
static constexpr uint8_t BL0906_MODE2 = 0x97;  // 工作模式寄存器2
```
- 添加AC_FREQ_SEL位掩码定义
```cpp
static constexpr uint32_t MODE2_AC_FREQ_SEL_MASK = 0x800000;  // 第23位
static constexpr uint32_t MODE2_AC_FREQ_50HZ = 0x000000;     // 50Hz
static constexpr uint32_t MODE2_AC_FREQ_60HZ = 0x800000;     // 60Hz
```

#### 3.2 扩展数据转换函数
**文件**: `bl0906_factory.cpp`
- 在`convert_raw_to_value()`函数中添加MODE2寄存器处理
- 在`is_unsigned_register()`函数中添加MODE2寄存器识别

### 第二阶段：频率检测和适配逻辑

#### 3.3 添加频率适配配置选项
**文件**: `bl0906_factory.h`
- 添加频率适配模式枚举和成员变量
```cpp
public:
  enum class FreqAdaptMode {
    OFF,    // 禁用频率适配，使用芯片默认50Hz
    AUTO,   // 自动检测频率并设置
    HZ60    // 强制设置为60Hz，不检测
  };

private:
  FreqAdaptMode freq_adapt_mode_ = FreqAdaptMode::AUTO;  // 频率适配模式
  bool freq_adapted_ = false;                            // 频率适配完成标志
  
  // 硬编码的检测参数
  static constexpr float FREQ_DETECT_THRESHOLD_LOW = 55.0f;   // 60Hz检测下限
  static constexpr float FREQ_DETECT_THRESHOLD_HIGH = 65.0f;  // 60Hz检测上限
  static constexpr uint8_t FREQ_DETECTION_SAMPLES = 3;        // 频率检测采样次数
```

- 添加公共配置方法
```cpp
public:
  void set_freq_adapt_mode(FreqAdaptMode mode);
```

#### 3.4 实现频率检测方法
**文件**: `bl0906_factory.cpp`
- 添加多次采样平均的频率检测方法
```cpp
private:
  float detect_grid_frequency();
  bool set_ac_frequency_mode(bool is_60hz);
  uint32_t read_mode2_register();
  bool write_mode2_register(uint32_t value);
```

#### 3.5 实现MODE2寄存器操作方法
- 读取MODE2寄存器完整24位数据
- 修改AC_FREQ_SEL位并写回
- 添加寄存器操作错误处理

### 第三阶段：集成到初始化流程

#### 3.6 修改setup()方法
**文件**: `bl0906_factory.cpp`
- 在通信适配器初始化完成后添加频率适配步骤
- 位置：校准数据加载之前，确保芯片处于正确的工作模式

```cpp
// 在setup()方法中添加（通信适配器初始化后）
if (freq_adapt_mode_ != FreqAdaptMode::OFF && !freq_adapted_) {
    switch (freq_adapt_mode_) {
        case FreqAdaptMode::AUTO:
            ESP_LOGI(FACTORY_TAG, "开始电网频率自动检测...");
            {
                float detected_freq = detect_grid_frequency();
                if (detected_freq > 0) {
                    bool is_60hz = (detected_freq >= FREQ_DETECT_THRESHOLD_LOW && 
                                   detected_freq <= FREQ_DETECT_THRESHOLD_HIGH);
                    
                    ESP_LOGI(FACTORY_TAG, "检测到电网频率: %.2fHz，设置为%s模式", 
                            detected_freq, is_60hz ? "60Hz" : "50Hz");
                            
                    if (set_ac_frequency_mode(is_60hz)) {
                        freq_adapted_ = true;
                        ESP_LOGI(FACTORY_TAG, "电网频率自动适配完成");
                    } else {
                        ESP_LOGW(FACTORY_TAG, "电网频率适配失败，使用默认50Hz模式");
                    }
                } else {
                    ESP_LOGW(FACTORY_TAG, "频率检测失败，使用默认50Hz模式");
                }
            }
            break;
            
        case FreqAdaptMode::HZ60:
            ESP_LOGI(FACTORY_TAG, "强制设置为60Hz模式...");
            if (set_ac_frequency_mode(true)) {
                freq_adapted_ = true;
                ESP_LOGI(FACTORY_TAG, "强制60Hz模式设置完成");
            } else {
                ESP_LOGW(FACTORY_TAG, "60Hz模式设置失败，使用默认50Hz模式");
            }
            break;
            
        default:
            break;
    }
}
```

### 第四阶段：Python配置接口

#### 3.7 扩展Python配置
**文件**: `__init__.py`
- 添加频率适配模式配置选项
```python
CONF_FREQ_ADAPT = "freq_adapt"

# 频率适配模式枚举
FreqAdaptMode = {
    "off": "OFF",     # 禁用频率适配
    "auto": "AUTO",   # 自动检测频率
    "60": "HZ60"      # 强制60Hz
}

CONFIG_SCHEMA = cv.Schema({
    # ... 现有配置 ...
    cv.Optional(CONF_FREQ_ADAPT, default="auto"): cv.enum(FreqAdaptMode, upper=False),
})
```

- 在配置生成代码中添加相应的C++方法调用
```python
# 在配置函数中添加
if CONF_FREQ_ADAPT in config:
    freq_adapt_mode = config[CONF_FREQ_ADAPT]
    cg.add(var.set_freq_adapt_mode(getattr(bl0906_factory_ns.FreqAdaptMode, FreqAdaptMode[freq_adapt_mode])))
```

### 第五阶段：测试和文档

#### 3.8 调试和测试功能
- 添加详细的日志输出，记录：
  - 频率检测过程和结果
  - MODE2寄存器读写操作
  - 频率适配成功/失败状态
- 实现错误处理和恢复机制
- 添加配置验证（阈值合理性检查）

#### 3.9 文档更新
- 更新组件文档，说明新增的自动频率适配功能
- 提供YAML配置示例
- 添加故障排除指南

## 4. 实现细节

### 4.1 频率检测算法
```cpp
float BL0906Factory::detect_grid_frequency() {
    if (!comm_adapter_) {
        ESP_LOGE(FACTORY_TAG, "通信适配器未设置，无法检测频率");
        return 0.0f;
    }
    
    float freq_sum = 0.0f;
    int valid_samples = 0;
    
         ESP_LOGI(FACTORY_TAG, "开始采样检测频率，采样次数: %d", FREQ_DETECTION_SAMPLES);
     
     for (int i = 0; i < FREQ_DETECTION_SAMPLES; i++) {
        bool success = false;
        int32_t freq_raw = send_read_command_and_receive(BL0906_FREQUENCY, &success);
        
        if (success && freq_raw > 0) {
            float freq = convert_raw_to_value(BL0906_FREQUENCY, freq_raw);
            if (freq > 30.0f && freq < 80.0f) {  // 合理的频率范围
                freq_sum += freq;
                valid_samples++;
                ESP_LOGD(FACTORY_TAG, "频率采样 %d: %.2fHz (原始值: %d)", 
                        i + 1, freq, freq_raw);
            }
        }
        
        // 采样间隔
        delay(100);
    }
    
    if (valid_samples > 0) {
        float avg_freq = freq_sum / valid_samples;
                 ESP_LOGI(FACTORY_TAG, "频率检测完成: %.2fHz (有效采样: %d/%d)", 
                avg_freq, valid_samples, FREQ_DETECTION_SAMPLES);
        return avg_freq;
    }
    
    ESP_LOGE(FACTORY_TAG, "频率检测失败：无有效采样");
    return 0.0f;
}
```

### 4.2 MODE2寄存器操作
```cpp
bool BL0906Factory::set_ac_frequency_mode(bool is_60hz) {
    ESP_LOGI(FACTORY_TAG, "设置AC频率模式为: %s", is_60hz ? "60Hz" : "50Hz");
    
    // 解除写保护
    if (!turn_off_write_protect()) {
        ESP_LOGE(FACTORY_TAG, "无法解除写保护");
        return false;
    }
    
    // 读取当前MODE2寄存器值
    uint32_t current_mode2 = read_mode2_register();
    if (current_mode2 == 0xFFFFFFFF) {  // 读取失败标志
        ESP_LOGE(FACTORY_TAG, "读取MODE2寄存器失败");
        return false;
    }
    
    // 修改AC_FREQ_SEL位
    uint32_t new_mode2 = current_mode2;
    if (is_60hz) {
        new_mode2 |= MODE2_AC_FREQ_SEL_MASK;   // 设置第23位为1
    } else {
        new_mode2 &= ~MODE2_AC_FREQ_SEL_MASK;  // 清除第23位为0
    }
    
    // 如果值没有变化，直接返回成功
    if (new_mode2 == current_mode2) {
        ESP_LOGI(FACTORY_TAG, "频率模式已经是%s，无需修改", is_60hz ? "60Hz" : "50Hz");
        return true;
    }
    
    // 写入新值
    if (write_mode2_register(new_mode2)) {
        ESP_LOGI(FACTORY_TAG, "MODE2寄存器更新成功: 0x%06X -> 0x%06X", 
                current_mode2, new_mode2);
        return true;
    } else {
        ESP_LOGE(FACTORY_TAG, "MODE2寄存器写入失败");
        return false;
    }
}
```

## 5. 配置示例

### 5.1 启用自动频率检测（默认）
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: auto  # 默认值，可省略，自动检测频率并设置
```

### 5.2 强制设置为60Hz
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: 60  # 不检测，直接设置为60Hz模式
```

### 5.3 禁用频率适配
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: off  # 禁用频率适配，使用芯片默认50Hz模式
```

## 6. 注意事项

### 6.1 安全考虑
- 频率适配只在首次初始化时执行，避免运行时频繁修改
- 检测参数硬编码，避免错误配置
- 失败时回退到安全的默认50Hz模式

### 6.2 性能考虑
- 自动检测模式会增加启动时间，但只执行一次
- 强制60Hz模式启动速度快，无需检测延迟
- 缓存适配结果，避免重复检测

### 6.3 兼容性
- 不影响现有功能，向后兼容
- 默认启用自动检测模式
- 保持代码整洁，移除不需要的向后兼容代码

## 7. 验收标准

1. **功能正确性**：能够正确检测50Hz和60Hz电网频率，强制60Hz模式工作正常
2. **配置简洁性**：支持auto、60、off三种模式，配置简单明了
3. **错误处理**：异常情况下能够安全回退
4. **日志完整性**：提供充分的调试信息
5. **性能要求**：初始化延迟控制在可接受范围内
6. **代码质量**：代码整洁，参数硬编码，无复杂配置

## 8. 开发时间估算

- **第一阶段**：1-2小时（寄存器定义）
- **第二阶段**：2-3小时（检测逻辑实现，参数硬编码简化）
- **第三阶段**：1-2小时（集成到初始化流程）
- **第四阶段**：1-2小时（Python配置接口，简化为3个选项）
- **第五阶段**：2-3小时（测试和文档）

**总计估算**：7-12小时 