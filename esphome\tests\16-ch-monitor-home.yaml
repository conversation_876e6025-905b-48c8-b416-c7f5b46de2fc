packages: 
  # bl0906: !include packages/16ch_home_6ch_calib.yaml
  # bl0910: !include packages/16ch_home_10ch_calib.yaml
  bl0906: !include packages/16ch_home_bl0906_calib.yaml
substitutions:
  name: "16-ch-monitor-home"
  friendly_name: "16-ch-energy-monitor-home"
  ch1: "Light"   #可根据需要修改名称
  ch2: "Kitchen Roaster"   #可根据需要修改名称
  ch3: "Bedroom1 Plug"    #可根据需要修改名称
  ch4: "Washing2 Plug"    #可根据需要修改名称
  ch5: "Dining Plug"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
  ch7: "ch_7"     #可根据需要修改名称
  ch8: "ch_8"     #可根据需要修改名称
  ch9: "ch_9"     #可根据需要修改名称
  ch10: "ch_10"     #可根据需要修改名称
  ch11: "AC exaust"   #可根据需要修改名称
  ch12: "Kitchen Vaccum"   #可根据需要修改名称
  ch13: "Cinema"    #可根据需要修改名称
  ch14: "Cabinets"    #可根据需要修改名称
  ch15: "AC compressor"  #可根据需要修改名称
  ch16: "ch_16"     #可根据需要修改名称
esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
  # on_boot:
  #   priority: 600
  #   then:
      # - uart.write: 
      #     data: [0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7]
      #     id: uart_bus1
      # - uart.write: 
      #     data: [0xCA, 0x76, 0x14, 0x07, 0x00, 0x6E]
      #     id: uart_bus1
      # - uart.write: 
      #     data: [0xCA, 0x9E, 0x00, 0x00, 0x00, 0x61]
      #     id: uart_bus1
      # - uart.write: 
      #     data: [0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7]
      #     id: uart_bus2
      # - uart.write: 
      #     data: [0xCA, 0x76, 0x14, 0x07, 0x00, 0x6E]
      #     id: uart_bus2
      # - uart.write: 
      #     data: [0xCA, 0x9E, 0x00, 0x00, 0x00, 0x61]
      #     id: uart_bus2
preferences:
  flash_write_interval: 10min
esp32:
  board: esp32dev
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
  logs:
    sensor: WARN
  #   mqtt.component: WARN
  #   mqtt.client: ERROR
  baud_rate: 0

# Enable Home Assistant API
api:
  encryption:
    key: "c8OIaeVFYiA5olgZlIJnxVjWeIISZb2l5SKs6nUI1fE="

ota:
  - platform: esphome
# mqtt:
#   broker: ************
#   username: mqtt
#   password: mqtt
#   client_id: 16ch
#   discover_ip: no
#   discovery: no
#   discovery_retain: no
#   log_topic: 16ch/log
#     # level: debug
#   birth_message:
#     topic: myavailability/topic
#     payload: online
#   will_message:
#     topic: myavailability/topic
#     payload: offline
#   shutdown_message:
#     topic: myavailability/topic
#     payload: shutdown
#   reboot_timeout: 0s
#   topic_prefix: 16ch
  
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "16-Ch-Monitor-Home"
    password: ""

captive_portal:
# globals:
#   - id: ch1_energy_
#     type: float
#     restore_value: yes
#   - id: ch1_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch2_energy_
#     type: float
#     restore_value: yes
#   - id: ch2_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch3_energy_
#     type: float
#     restore_value: yes
#   - id: ch3_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch4_energy_
#     type: float
#     restore_value: yes
#   - id: ch4_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch5_energy_
#     type: float
#     restore_value: yes
#   - id: ch5_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch6_energy_
#     type: float
#     restore_value: yes
#   - id: ch6_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch7_energy_
#     type: float
#     restore_value: yes
#   - id: ch7_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch8_energy_
#     type: float
#     restore_value: yes
#   - id: ch8_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch9_energy_
#     type: float
#     restore_value: yes
#   - id: ch9_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: ch10_energy_
#     type: float
#     restore_value: yes
#   - id: ch10_energy_last  #energy from last power cycle
#     type: float
#     restore_value: yes
#   - id: chs10_energy_ 
#     type: float
#     restore_value: yes
#   - id: chs10_energy_last   #energy from last power cycle
#     type: float
#     restore_value: yes
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: calibrate
      name: "Calibrate"
      sorting_weight: 0
    - id: bl0910_sensors
      name: "BL0910 Basic Sensors"
      sorting_weight: 5
    - id: bl0910_current
      name: "BL0910 Current (CH1-10)"
      sorting_weight: 10
    - id: bl0910_power
      name: "BL0910 Power (CH1-10)"
      sorting_weight: 15
    - id: bl0910_energy
      name: "BL0910 Energy (CH1-10)"
      sorting_weight: 20
    - id: bl0910_total_energy
      name: "BL0910 Total Energy (CH1-10)"
      sorting_weight: 25
    - id: bl0910_energy_last
      name: "BL0910 Energy Last (CH1-10)"
      sorting_weight: 30
    - id: pzem_004T
      name: "PZEM-004T Sensors"
      sorting_weight: 35
    - id: current
      name: "BL0906 Current (CH11-16)"
      sorting_weight: 40
    - id: power
      name: "BL0906 Power (CH11-16)"
      sorting_weight: 45
    - id: energy
      name: "BL0906 Energy (CH11-16)"
      sorting_weight: 50
    - id: energy_stats
      name: "BL0906 Energy Statistics"
      sorting_weight: 55
    - id: bl0906_calibration
      name: "BL0906 Calibration Settings"
      sorting_weight: 60
    - id: bl0906_controls
      name: "BL0906 Control Buttons"
      sorting_weight: 65
    - id: system_monitoring
      name: "System Monitoring"
      sorting_weight: 70
    - id: network_info
      name: "Network Information"
      sorting_weight: 75
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 80
# external_components:
#   - source:
#       type: git
#       url: https://github.com/carrot8848/ESPHome
#       ref: main
  # - source: github://dentra/esphome-components
#   - source: github://oxan/esphome-stream-server
# stream_server:
#   uart_id: uart_bus1
#   port: 6638
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
status_led:
  pin: 
    number: GPIO13
    inverted: true
uart:
  - id: uart_bus1
    rx_pin: 33
    tx_pin: 32
    baud_rate: 9600
  - id: uart_bus2
    rx_pin: 16
    tx_pin: 17
    baud_rate: 19200
  - id: uart_bus3
    rx_pin: 14
    tx_pin: 27
    baud_rate: 9600
modbus:
  - uart_id: uart_bus3
    id: mod_bus1

sensor:
  # - platform: BL0910
  #   id: sensor_bl0910
  #   uart_id: uart_bus2
  #   update_interval: 10s   
  #   Frequency:
  #     name: 'Frequency'
  #     web_server:
  #       sorting_group_id: bl0910_sensors
  #   Temperature:
  #     name: 'Temperature'
  #     web_server:
  #       sorting_group_id: bl0910_sensors
  #   Voltage:
  #     name: 'bl0910 Voltage'
  #     web_server:
  #       sorting_group_id: bl0910_sensors
  #   Current_1:
  #     name: "${ch1} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_2:
  #     name: "${ch2} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_3:
  #     name: "${ch3} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_4:
  #     name: "${ch4} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_5:
  #     name: "${ch5} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_6:
  #     name: "${ch6} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_7:
  #     name: "${ch7} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_8:
  #     name: "${ch8} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_9:
  #     name: "${ch9} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Current_10:
  #     name: "${ch10} current"
  #     web_server:
  #       sorting_group_id: bl0910_current
  #   Power_1:
  #     name: "${ch1} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_2:
  #     name: "${ch2} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_3:
  #     name: "${ch3} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_4:
  #     name: "${ch4} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_5:
  #     name: "${ch5} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_6:
  #     name: "${ch6} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_7:
  #     name: "${ch7} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_8:
  #     name: "${ch8} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_9:
  #     name: "${ch9} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_10:
  #     name: "${ch10} power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Power_sum:
  #     name: "10-ch sum power"
  #     web_server:
  #       sorting_group_id: bl0910_power
  #   Energy_1: 
  #     id: ch1_energy
  #     name: "${ch1} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch1_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch1_energy_
  #                   value: !lambda return id(ch1_energy_last) + x;
  #               - logger.log:
  #                   format: "ch1 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch1_energy_last
  #                   value: !lambda return id(ch1_energy_);
  #               - logger.log:
  #                   format: "ch1 energy last refreshed!"
  #                   level: WARN
  #   Energy_2: 
  #     id: ch2_energy
  #     name: "${ch2} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch2_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch2_energy_
  #                   value: !lambda return id(ch2_energy_last) + x;
  #               - logger.log:
  #                   format: "ch2 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch2_energy_last
  #                   value: !lambda return id(ch2_energy_);
  #               - logger.log:
  #                   format: "ch2 energy last refreshed!"
  #                   level: WARN
  #   Energy_3: 
  #     id: ch3_energy
  #     name: "${ch3} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch3_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch3_energy_
  #                   value: !lambda return id(ch3_energy_last) + x;
  #               - logger.log:
  #                   format: "ch3 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch3_energy_last
  #                   value: !lambda return id(ch3_energy_);
  #               - logger.log:
  #                   format: "ch3 energy last refreshed!"
  #                   level: WARN
  #   Energy_4: 
  #     id: ch4_energy
  #     name: "${ch4} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch4_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch4_energy_
  #                   value: !lambda return id(ch4_energy_last) + x;
  #               - logger.log:
  #                   format: "ch4 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch4_energy_last
  #                   value: !lambda return id(ch4_energy_);
  #               - logger.log:
  #                   format: "ch4 energy last refreshed!"
  #                   level: WARN
  #   Energy_5: 
  #     id: ch5_energy
  #     name: "${ch5} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch5_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch5_energy_
  #                   value: !lambda return id(ch5_energy_last) + x;
  #               - logger.log:
  #                   format: "ch5 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch5_energy_last
  #                   value: !lambda return id(ch5_energy_);
  #               - logger.log:
  #                   format: "ch5 energy last refreshed!"
  #                   level: WARN
  #   Energy_6: 
  #     id: ch6_energy
  #     name: "${ch6} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch6_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch6_energy_
  #                   value: !lambda return id(ch6_energy_last) + x;
  #               - logger.log:
  #                   format: "ch6 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch6_energy_last
  #                   value: !lambda return id(ch6_energy_);
  #               # - logger.log:
  #               #     format: "ch6 energy last refreshed!"
  #               #     level: WARN
  #   Energy_7: 
  #     id: ch7_energy
  #     name: "${ch7} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch7_energy
  #                 above: 0.01
  #             then: 
  #               -  globals.set: 
  #                   id: ch7_energy_
  #                   value: !lambda return id(ch7_energy_last) + x;
  #               - logger.log:
  #                   format: "ch7 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch7_energy_last
  #                   value: !lambda return id(ch7_energy_);
  #               # - logger.log:
  #               #     format: "ch7 energy last refreshed!"
  #               #     level: WARN
  #   Energy_8: 
  #     id: ch8_energy
  #     name: "${ch8} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch8_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch8_energy_
  #                   value: !lambda return id(ch8_energy_last) + x;
  #               - logger.log:
  #                   format: "ch8 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch8_energy_last
  #                   value: !lambda return id(ch8_energy_);
  #               # - logger.log:
  #               #     format: "ch8 energy last refreshed!"
  #               #     level: WARN
  #   Energy_9: 
  #     id: ch9_energy
  #     name: "${ch9} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch9_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch9_energy_
  #                   value: !lambda return id(ch9_energy_last) + x;
  #               - logger.log:
  #                   format: "ch9 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch9_energy_last
  #                   value: !lambda return id(ch9_energy_);
  #               # - logger.log:
  #               #     format: "ch9 energy last refreshed!"
  #               #     level: WARN
  #   Energy_10: 
  #     id: ch10_energy
  #     name: "${ch10} energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: ch10_energy
  #                 above: 0.01
  #             then: 
  #               - globals.set: 
  #                   id: ch10_energy_
  #                   value: !lambda return id(ch10_energy_last) + x;
  #               - logger.log:
  #                   format: "ch10 energy refreshed normally"
  #                   level: debug
  #             else:
  #               - globals.set: 
  #                   id: ch10_energy_last
  #                   value: !lambda return id(ch10_energy_);
  #               # - logger.log:
  #               #     format: "ch10 energy last refreshed!"
  #               #     level: WARN
  #   Energy_sum: 
  #     id: chs10_energy_sum
  #     name: "10-ch energy"
  #     web_server:
  #       sorting_group_id: bl0910_energy
  #     on_value: 
  #       then:
  #         - if:
  #             condition:
  #               sensor.in_range: 
  #                 id: chs10_energy_sum
  #                 above: 0.01
  #             then: 
  #               globals.set: 
  #                 id: chs10_energy_
  #                 value: !lambda return id(chs10_energy_last) + x;
  #             else:
  #               - globals.set: 
  #                   id: chs10_energy_last
  #                   value: !lambda return id(chs10_energy_);
  # - platform: template
  #   name: "${ch1} Total Energy"
  #   id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch1_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch2} Total Energy"
  #   id: ch2_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch2_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch3} Total Energy"
  #   id: ch3_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch3_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch4} Total Energy"
  #   id: ch4_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch4_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch5} Total Energy"
  #   id: ch5_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch5_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch6} Total Energy"
  #   id: ch6_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch6_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch7} Total Energy"
  #   id: ch7_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch7_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch8} Total Energy"
  #   id: ch8_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch8_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch9} Total Energy"
  #   id: ch9_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch9_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
  # - platform: template
  #   name: "${ch10} Total Energy"
  #   id: ch10_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch10_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy
 
  # - platform: template
  #   name: "10 chs Total Energy"
  #   id: chs10_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(chs10_energy_);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_total_energy

  # - platform: template
  #   name: "${ch1} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch1_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch2} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch2_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch3} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch3_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch4} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch4_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch5} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch5_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch6} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch6_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch7} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch7_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch8} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch8_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch9} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch9_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last
  # - platform: template
  #   name: "${ch10} energy_last"
  #   # id: ch1_total_energy
  #   unit_of_measurement: kWh
  #   device_class: energy
  #   state_class: total_increasing
  #   accuracy_decimals: 2
  #   lambda: |-
  #     return id(ch10_energy_last);
  #   update_interval: 10s
  #   web_server:
  #     sorting_group_id: bl0910_energy_last

  - platform: pzemac
    id: sensor_pzem
    update_interval: 10s
    modbus_id: mod_bus1
    current:
      name: "PZEM Current"
      web_server:
        sorting_group_id: pzem_004T
    voltage:
      name: "PZEM Voltage"
      web_server:
        sorting_group_id: pzem_004T
    energy:
      name: "PZEM Energy"
      id: pzem_energy
      filters:
        # Multiplication factor from Wh to kWh is 0.001
        - multiply: 0.001
      unit_of_measurement: kWh
      web_server:
        sorting_group_id: pzem_004T
    power:
      name: "PZEM Power"
      id: pzemac_power
      filters:
        # Multiplication factor from W to kW is 0.001
       - multiply: 0.001
      unit_of_measurement: kW
      web_server:
        sorting_group_id: pzem_004T
    frequency:
      name: "PZEM Frequency"
      web_server:
        sorting_group_id: pzem_004T
    power_factor:
      name: "PZEM Power Factor"
      web_server:
        sorting_group_id: pzem_004T
    
  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    web_server:
      sorting_group_id: network_info

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    web_server:
      sorting_group_id: network_info

  - platform: debug
    free:
      name: "Heap Free"
      web_server:
        sorting_group_id: system_monitoring
    block:
      name: "Heap Max Block"
      web_server:
        sorting_group_id: system_monitoring
    loop_time:
      name: "Loop Time"
      web_server:
        sorting_group_id: system_monitoring
debug:
  update_interval: 5s
switch:
  - platform: restart
    name: "${name} controller Restart"
    web_server:
      sorting_group_id: miscellaneous
  - platform: factory_reset
    name: Restart with Factory Default Settings
    web_server:
      sorting_group_id: miscellaneous

time:
  - platform: homeassistant
    id: my_time

text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
      web_server:
        sorting_group_id: network_info
    ssid:
      name: Energy_meter Connected SSID
      web_server:
        sorting_group_id: network_info
    bssid:
      name: Energy_meter Connected BSSID
      web_server:
        sorting_group_id: network_info
    mac_address:
      name: Energy_meter Mac Wifi Address
      web_server:
        sorting_group_id: network_info
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address
      web_server:
        sorting_group_id: network_info
  - platform: debug
    device:
      name: "Device Info"
      web_server:
        sorting_group_id: system_monitoring
    reset_reason:
      name: "Reset Reason"
      web_server:
        sorting_group_id: system_monitoring 