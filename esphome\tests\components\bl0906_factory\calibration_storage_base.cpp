#include "calibration_storage_base.h"
#include "esphome/core/hal.h"
#include <string>
#include <vector>
#include <algorithm>
#include <cstring>

namespace esphome {
namespace bl0906_factory {

// 公共接口的默认实现，使用模板方法模式
bool CalibrationStorageBase::read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) {
    // 验证实例ID
    if (!validate_instance_id(instance_id)) {
        log_data_validation_error("instance_id", instance_id, MAX_VALID_INSTANCE_ID);
        return false;
    }
    
    // 读取原始数据
    uint8_t buffer[512]; // 足够大的缓冲区
    size_t buffer_size = sizeof(buffer);
    StorageResult result = read_raw_data(instance_id, buffer, buffer_size);
    
    if (result != StorageResult::SUCCESS) {
        if (result == StorageResult::INSTANCE_NOT_FOUND) {
            ESP_LOGD(get_log_tag(), "实例 0x%08X 不存在", instance_id);
        } else {
            log_storage_error("读取", result, "实例数据读取失败");
        }
        return false;
    }
    
    // 反序列化数据
    if (!deserialize_entries(buffer, buffer_size, entries)) {
        log_storage_error("反序列化", StorageResult::INVALID_DATA, "数据格式错误");
        return false;
    }
    
    log_instance_operation("读取", instance_id, true, 
                          (std::string("成功读取 ") + std::to_string(entries.size()) + " 个条目").c_str());
    return true;
}

bool CalibrationStorageBase::write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) {
    // 验证实例ID和条目数据
    if (!validate_instance_id(instance_id)) {
        log_data_validation_error("instance_id", instance_id, MAX_VALID_INSTANCE_ID);
        return false;
    }
    
    if (!validate_entries(entries)) {
        log_storage_error("验证", StorageResult::INVALID_DATA, "校准条目数据无效");
        return false;
    }
    
    // 序列化数据
    uint8_t buffer[512];
    size_t buffer_size = serialize_entries(entries, buffer, sizeof(buffer));
    
    if (buffer_size == 0) {
        log_storage_error("序列化", StorageResult::INVALID_DATA, "数据序列化失败");
        return false;
    }
    
    // 写入原始数据
    StorageResult result = write_raw_data(instance_id, buffer, buffer_size);
    
    if (result != StorageResult::SUCCESS) {
        log_storage_error("写入", result, "实例数据写入失败");
        return false;
    }
    
    log_instance_operation("写入", instance_id, true,
                          (std::string("成功写入 ") + std::to_string(entries.size()) + " 个条目").c_str());
    return true;
}

bool CalibrationStorageBase::delete_instance(uint32_t instance_id) {
    // 验证实例ID
    if (!validate_instance_id(instance_id)) {
        log_data_validation_error("instance_id", instance_id, MAX_VALID_INSTANCE_ID);
        return false;
    }
    
    // 删除原始数据
    StorageResult result = delete_raw_data(instance_id);
    
    if (result != StorageResult::SUCCESS && result != StorageResult::INSTANCE_NOT_FOUND) {
        log_storage_error("删除", result, "实例数据删除失败");
        return false;
    }
    
    log_instance_operation("删除", instance_id, true);
    return true;
}

// 通用验证方法
bool CalibrationStorageBase::validate_instance_id(uint32_t instance_id) const {
    return instance_id >= MIN_VALID_INSTANCE_ID && instance_id <= MAX_VALID_INSTANCE_ID;
}

bool CalibrationStorageBase::validate_entries(const std::vector<CalibrationEntry>& entries) const {
    if (entries.empty()) {
        return false; // 空条目列表无效
    }
    
    StorageResult count_result = validate_entries_count(entries.size(), get_max_entries_per_instance());
    if (count_result != StorageResult::SUCCESS) {
        return false;
    }
    
    // 检查每个条目的有效性
    for (const auto& entry : entries) {
        if (entry.register_addr == 0) {
            ESP_LOGW(get_log_tag(), "发现无效的寄存器地址: 0x%02X", entry.register_addr);
            return false;
        }
        // 可以添加更多寄存器地址的验证逻辑
    }
    
    return true;
}

StorageResult CalibrationStorageBase::validate_entries_count(size_t count, size_t max_count) const {
    if (count == 0) {
        return StorageResult::INVALID_DATA;
    }
    if (count > max_count) {
        return StorageResult::STORAGE_FULL;
    }
    return StorageResult::SUCCESS;
}

// 数据序列化/反序列化方法
size_t CalibrationStorageBase::serialize_entries(const std::vector<CalibrationEntry>& entries, 
                                                uint8_t* buffer, size_t buffer_size) const {
    // 计算需要的缓冲区大小：2字节条目数 + 条目数据
    size_t required_size = sizeof(uint16_t) + entries.size() * ENTRY_SERIALIZED_SIZE;
    
    if (buffer_size < required_size) {
        ESP_LOGE(get_log_tag(), "缓冲区太小: 需要%d字节，提供%d字节", required_size, buffer_size);
        return 0;
    }
    
    // 写入条目数量
    uint16_t entry_count = static_cast<uint16_t>(entries.size());
    memcpy(buffer, &entry_count, sizeof(uint16_t));
    
    // 写入条目数据
    uint8_t* data_ptr = buffer + sizeof(uint16_t);
    for (size_t i = 0; i < entries.size(); i++) {
        data_ptr[i * ENTRY_SERIALIZED_SIZE] = entries[i].register_addr;
        data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] = static_cast<uint8_t>(entries[i].value >> 8);
        data_ptr[i * ENTRY_SERIALIZED_SIZE + 2] = static_cast<uint8_t>(entries[i].value & 0xFF);
    }
    
    return required_size;
}

size_t CalibrationStorageBase::serialize_entries_with_instance_id(uint32_t instance_id, 
                                                                 const std::vector<CalibrationEntry>& entries, 
                                                                 uint8_t* buffer, size_t buffer_size) const {
    // 计算需要的缓冲区大小：4字节实例ID + 条目数据
    size_t required_size = sizeof(uint32_t) + entries.size() * ENTRY_SERIALIZED_SIZE;
    
    if (buffer_size < required_size) {
        ESP_LOGE(get_log_tag(), "缓冲区太小: 需要%d字节，提供%d字节", required_size, buffer_size);
        return 0;
    }
    
    // 写入实例ID
    memcpy(buffer, &instance_id, sizeof(uint32_t));
    
    // 写入条目数据
    uint8_t* data_ptr = buffer + sizeof(uint32_t);
    for (size_t i = 0; i < entries.size(); i++) {
        data_ptr[i * ENTRY_SERIALIZED_SIZE] = entries[i].register_addr;
        data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] = static_cast<uint8_t>(entries[i].value >> 8);
        data_ptr[i * ENTRY_SERIALIZED_SIZE + 2] = static_cast<uint8_t>(entries[i].value & 0xFF);
    }
    
    return required_size;
}

bool CalibrationStorageBase::deserialize_entries(const uint8_t* buffer, size_t buffer_size, 
                                                std::vector<CalibrationEntry>& entries) const {
    if (buffer_size < sizeof(uint16_t)) {
        ESP_LOGE(get_log_tag(), "缓冲区太小");
        return false;
    }
    
    entries.clear();
    
    // 智能格式检测：检查前2字节是否为合理的条目数量
    uint16_t potential_count;
    memcpy(&potential_count, buffer, sizeof(uint16_t));
    
    // 如果前2字节看起来像条目数量，且数据大小匹配，则认为是Preferences格式
    size_t expected_preferences_size = sizeof(uint16_t) + potential_count * ENTRY_SERIALIZED_SIZE;
    bool is_preferences_format = (
        potential_count > 0 && 
        potential_count <= get_max_entries_per_instance() && 
        buffer_size >= expected_preferences_size &&
        buffer_size <= expected_preferences_size + 16  // 允许一些填充
    );
    
    if (is_preferences_format) {
        // Preferences格式：条目数量 + 条目数据
        ESP_LOGD(get_log_tag(), "检测到Preferences格式，条目数: %d", potential_count);
        
        entries.reserve(potential_count);
        const uint8_t* data_ptr = buffer + sizeof(uint16_t);
        
        for (uint16_t i = 0; i < potential_count; i++) {
            CalibrationEntry entry;
            entry.register_addr = data_ptr[i * ENTRY_SERIALIZED_SIZE];
            entry.value = static_cast<int16_t>(
                (data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] << 8) | 
                data_ptr[i * ENTRY_SERIALIZED_SIZE + 2]
            );
            
            // 跳过空条目
            if (entry.register_addr != 0) {
                entries.push_back(entry);
            }
        }
    } else if (buffer_size > 4) {
        // EEPROM格式：实例ID + 条目数据（无条目数量字段）
        ESP_LOGD(get_log_tag(), "检测到EEPROM格式，数据大小: %d", buffer_size);
        
        const uint8_t* data_ptr = buffer + 4; // 跳过实例ID
        size_t max_entries = (buffer_size - 4) / ENTRY_SERIALIZED_SIZE;
        
        for (size_t i = 0; i < max_entries; i++) {
            uint8_t reg_addr = data_ptr[i * ENTRY_SERIALIZED_SIZE];
            if (reg_addr == 0) break; // 空条目，结束解析
            
            CalibrationEntry entry;
            entry.register_addr = reg_addr;
            entry.value = static_cast<int16_t>(
                (data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] << 8) | 
                data_ptr[i * ENTRY_SERIALIZED_SIZE + 2]
            );
            entries.push_back(entry);
        }
    } else {
        ESP_LOGE(get_log_tag(), "无法识别数据格式，大小: %d", buffer_size);
        return false;
    }
    
    ESP_LOGD(get_log_tag(), "反序列化成功，解析出 %d 个条目", entries.size());
    return true;
}

// 通用日志方法
void CalibrationStorageBase::log_instance_operation(const char* operation, uint32_t instance_id, 
                                                   bool success, const char* details) const {
    if (success) {
        ESP_LOGD(get_log_tag(), "%s实例 0x%08X %s", operation, instance_id, details ? details : "成功");
    } else {
        ESP_LOGE(get_log_tag(), "%s实例 0x%08X 失败: %s", operation, instance_id, details ? details : "未知错误");
    }
}

void CalibrationStorageBase::log_storage_error(const char* operation, StorageResult result, 
                                              const char* details) const {
    const char* result_str;
    
    switch (result) {
        case StorageResult::SUCCESS:
            result_str = "成功";
            break;
        case StorageResult::INSTANCE_NOT_FOUND:
            result_str = "实例不存在";
            break;
        case StorageResult::STORAGE_FULL:
            result_str = "存储空间已满";
            break;
        case StorageResult::INVALID_DATA:
            result_str = "数据无效";
            break;
        case StorageResult::IO_ERROR:
            result_str = "IO错误";
            break;
        case StorageResult::VERIFICATION_FAILED:
            result_str = "验证失败";
            break;
        default:
            result_str = "未知错误";
            break;
    }
    
    ESP_LOGE(get_log_tag(), "%s操作失败: %s (%s)", operation, result_str, details ? details : "");
}

void CalibrationStorageBase::log_data_validation_error(const char* field, uint32_t value, 
                                                      uint32_t max_value) const {
    ESP_LOGE(get_log_tag(), "数据验证失败: %s=0x%08X (最大值: 0x%08X)", field, value, max_value);
}

// 实例列表操作方法
bool CalibrationStorageBase::add_to_instance_list(std::vector<uint32_t>& instance_list, uint32_t instance_id) {
    auto it = std::find(instance_list.begin(), instance_list.end(), instance_id);
    if (it == instance_list.end()) {
        instance_list.push_back(instance_id);
        return true;
    }
    return false; // 已存在
}

bool CalibrationStorageBase::remove_from_instance_list(std::vector<uint32_t>& instance_list, uint32_t instance_id) {
    auto it = std::find(instance_list.begin(), instance_list.end(), instance_id);
    if (it != instance_list.end()) {
        instance_list.erase(it);
        return true;
    }
    return false; // 不存在
}

// 数据完整性检查
bool CalibrationStorageBase::check_data_integrity(const uint8_t* data, size_t size) const {
    if (data == nullptr || size == 0) {
        return false;
    }
    
    // 基本的数据完整性检查
    // 可以根据需要扩展更复杂的验证逻辑
    return true;
}

}  // namespace bl0906_factory
}  // namespace esphome 