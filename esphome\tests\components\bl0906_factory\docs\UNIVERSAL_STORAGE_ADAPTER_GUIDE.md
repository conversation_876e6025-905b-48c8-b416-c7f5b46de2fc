# BL0906Factory 通用存储适配器使用指南

## 概述

BL0906Factory 组件现在支持通用的存储适配器机制，可以在不修改按钮代码的情况下，轻松在不同存储类型之间切换。

## 支持的存储类型

### 1. Preference 存储 (默认)
- **类型标识**: `preference`
- **容量**: 几乎无限（使用ESP32的NVS分区）
- **最大实例数**: 16个
- **优点**: 简单、不需要外部硬件
- **缺点**: 无法脱离ESP32存在

### 2. I2C EEPROM 存储
- **类型标识**: `eeprom`
- **容量**: 根据EEPROM型号（256字节-2KB）
- **最大实例数**: 根据容量自动计算（通常1-8个实例）
- **优点**: 独立于ESP32，可物理备份和转移
- **缺点**: 需要外部EEPROM芯片

## 配置方法

### Preference 存储配置
```yaml
bl0906_factory:
  # 其他配置...
  calibration:
    enabled: true
    storage_type: preference  # 使用Preference存储
```

### I2C EEPROM 存储配置
```yaml
i2c:
  sda: 10
  scl: 18
  scan: true
  frequency: 400kHz
  id: i2c_bus

bl0906_factory:
  # 其他配置...
  calibration:
    enabled: true
    storage_type: eeprom      # 使用EEPROM存储
    eeprom_type: 24c02        # EEPROM型号
  # EEPROM的I2C配置
  i2c_id: i2c_bus
  address: 0x50
```

## 简化的按钮代码

所有存储操作都已封装在BL0906Factory的方法中，YAML中的按钮代码极其简洁：

```yaml
button:
  - platform: template
    name: "Read Calibration Data from Flash"
    on_press:
      - lambda: |-
          auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          if (bl0906 != nullptr) {
            bl0906->read_and_display_calibration_data();
          }

  - platform: template
    name: "Show All Instance Calibration Data"
    on_press:
      - lambda: |-
          auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          if (bl0906 != nullptr) {
            bl0906->show_all_instances_calibration_data();
          }

  - platform: template
    name: "Show Storage Status"
    on_press:
      - lambda: |-
          auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          if (bl0906 != nullptr) {
            bl0906->show_storage_status();
          }

  - platform: template
    name: "Clear Storage"
    on_press:
      - lambda: |-
          auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          if (bl0906 != nullptr) {
            bl0906->clear_calibration_storage();
          }
```

## 切换存储类型

要在不同存储类型间切换，只需：

1. **修改配置文件**：更改 `storage_type` 参数
2. **重新编译烧录**：无需修改任何按钮代码
3. **数据迁移**（如需要）：使用相应的读取/保存按钮

## 按钮功能说明

### 通用按钮（适配所有存储类型）
- **Read Calibration Data from Flash**: 读取当前实例的校准数据
- **Show All Instance Calibration Data**: 显示所有实例的校准数据  
- **Show Storage Status**: 显示存储状态信息
- **Clear Storage**: 清除存储中的所有数据

### 存储特定信息
- **Preference**: 显示实例数量和ID列表
- **EEPROM**: 额外显示容量、最大实例数、剩余槽位等详细信息

## 优势

1. **极简按钮代码**: YAML中只需一行方法调用，所有复杂逻辑都封装在C++中
2. **配置驱动**: 通过配置文件控制存储类型，无需修改任何按钮代码
3. **易于切换**: 在不同项目或需求中轻松切换存储方案，按钮代码完全不变
4. **扩展性好**: 添加新存储类型时，现有按钮无需修改
5. **维护简单**: 存储相关逻辑集中在C++代码中，便于调试和维护
6. **错误处理**: 统一的错误处理逻辑，无需在每个按钮中重复编写

## 实现原理

### 核心接口
```cpp
class CalibrationStorageInterface {
public:
    virtual bool init() = 0;
    virtual bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) = 0;
    virtual bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) = 0;
    virtual std::string get_storage_type() const = 0;
    // 其他方法...
};
```

### 工厂方法
```cpp
std::unique_ptr<CalibrationStorageInterface> BL0906Factory::create_storage_instance() {
    if (storage_type_ == "preference") {
        return std::make_unique<PreferenceCalibrationStorage>();
    } else if (storage_type_ == "eeprom") {
        return std::make_unique<I2CEEPROMCalibrationStorage>(i2c_parent_, eeprom_type_, i2c_address_);
    }
    // 其他存储类型...
}
```

## 注意事项

1. **数据兼容性**: 不同存储类型之间的数据结构相同，但不能直接迁移
2. **实例ID**: 建议为每个设备设置唯一的实例ID以避免冲突
3. **容量限制**: EEPROM存储有容量限制，需根据实际需求选择合适型号
4. **硬件依赖**: EEPROM存储需要正确连接I2C EEPROM芯片

## 扩展存储类型

要添加新的存储类型（如SPI Flash）：

1. **实现接口**: 创建新类继承 `CalibrationStorageInterface`
2. **注册工厂**: 在 `create_storage_instance()` 中添加判断逻辑
3. **更新配置**: 在Python组件中添加新的配置选项
4. **无需修改按钮**: 现有按钮代码自动支持新存储类型

这种设计提供了最大的灵活性和可维护性。 