# BL0906Factory CF_count 逻辑修改计划

## 修改概述

根据用户要求，需要修改电量持久化存储的逻辑，改变数据更新公式和保存触发机制。

## 当前实现分析

### 当前逻辑
```cpp
// 在 process_energy_persistence 方法中
uint32_t pulse_increment = current_count - last_cf_count_[i];
persistent_cf_count_[i] += pulse_increment;  // 累加增量模式
```

### 当前保存触发条件
1. 每100个脉冲增量触发保存
2. 芯片重启检测时设置 `last_cf_count_needs_save_` 标志
3. 在 `save_energy_data()` 中基于CF_count变化阈值(1000)判断是否保存

## 新的需求规格

### 1. 数据更新公式
```
persistent_cf_count[i] = current_count + last_cf_count[i]
```
**说明**: 从累加增量模式改为直接相加模式

### 2. 标志位触发逻辑
当 `last_cf_count_needs_save_ = true` 时：
- 将 `persistent_cf_count[i]` 赋值给 `last_cf_count[i]`
- 执行保存操作
- 更新 `saved_total_cf` 为当前6通道CF总和

### 3. 总和校验触发逻辑
每次读取芯片后：
- 计算当前6通道CF总和（`current_total_cf`）
- 若 `current_total_cf - saved_total_cf ≥ 1000`，触发保存
- 更新 `saved_total_cf`

## 代码修改计划

### 第一步：添加新的成员变量

在 `bl0906_factory.h` 中添加：
```cpp
// 新增变量：记录上次保存时的6通道CF总和-=
uint32_t saved_total_cf_ = 0;
```

### 第二步：修改 process_energy_persistence 方法

#### 2.1 修改数据更新公式
```cpp
// 原来的逻辑
persistent_cf_count_[i] += pulse_increment;

// 修改为新逻辑
persistent_cf_count_[i] = current_count + last_cf_count_[i];
```

#### 2.2 移除原有的保存触发逻辑
移除基于脉冲增量的保存触发：
```cpp
// 删除这部分代码
if (pulse_increment >= 100) {
  save_energy_data();
}
```

#### 2.3 添加标志位触发处理
在适当位置添加标志位处理逻辑：
```cpp
// 标志位触发逻辑
if (last_cf_count_needs_save_) {
  // 将 persistent_cf_count[i] 赋值给 last_cf_count[i]
  for (int j = 0; j < CHANNEL_COUNT; j++) {
    last_cf_count_[j] = persistent_cf_count_[j];
  }
  
  // 执行保存操作
  save_energy_data();
  
  // 更新 saved_total_cf 为当前6通道CF总和 - 直接内联计算
  saved_total_cf_ = 0;
  for (int j = 0; j < CHANNEL_COUNT; j++) {
    saved_total_cf_ += persistent_cf_count_[j];
  }
  ESP_LOGI(FACTORY_TAG, "更新saved_total_cf: %u", saved_total_cf_);
  
  // 重置标志位
  last_cf_count_needs_save_ = false;
}
```

### 第三步：添加总和校验触发逻辑

#### 3.1 在 process_energy_persistence 方法末尾添加
```cpp
// 总和校验触发逻辑 - 直接内联计算，无需辅助方法
uint32_t current_total_cf = 0;
for (int j = 0; j < CHANNEL_COUNT; j++) {
  current_total_cf += persistent_cf_count_[j];
}

if (current_total_cf >= saved_total_cf_ && 
    (current_total_cf - saved_total_cf_) >= 1000) {
  ESP_LOGI(FACTORY_TAG, "总和校验触发保存: 当前总和=%u, 上次保存=%u, 差值=%u", 
           current_total_cf, saved_total_cf_, current_total_cf - saved_total_cf_);
  
  save_energy_data();
  saved_total_cf_ = current_total_cf;  // 直接更新，无需额外方法
}
```

### 第四步：修改 save_energy_data 方法

#### 4.1 移除原有的变化检测逻辑
删除基于CF_count变化的保存判断：
```cpp
// 删除这些代码
static uint32_t last_total_cf_count = 0;
uint32_t current_total_cf_count = persistent_cf_count_[6];
bool cf_count_changed = abs((int32_t)(current_total_cf_count - last_total_cf_count)) >= MIN_CHANGE_THRESHOLD;

if (!cf_count_changed && !last_cf_count_needs_save_) {
  // ...跳过保存逻辑
}
```

#### 4.2 简化保存逻辑
```cpp
void BL0906Factory::save_energy_data() {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过保存");
    return;
  }

  current_save_count_++;
  ESP_LOGI(FACTORY_TAG, "执行CF_count数据保存，存储计数: %u", current_save_count_);

  // 准备保存数据结构... (保持现有的保存逻辑)
}
```

### 第五步：修改初始化逻辑

在 `load_energy_data()` 方法中添加 `saved_total_cf_` 的初始化：
```cpp
// 加载数据成功后，初始化 saved_total_cf_ - 直接内联计算
saved_total_cf_ = 0;
for (int i = 0; i < CHANNEL_COUNT; i++) {
  saved_total_cf_ += persistent_cf_count_[i];
}
ESP_LOGI(FACTORY_TAG, "初始化saved_total_cf: %u", saved_total_cf_);
```


## 修改后的工作流程

### 正常运行流程
1. 读取芯片CF_count值 (`current_count`)
2. 应用新公式：`persistent_cf_count[i] = current_count + last_cf_count[i]`
3. 检查标志位触发条件
4. 检查总和校验触发条件
5. 根据触发条件决定是否保存

### 芯片重启流程
1. 检测到芯片重启
2. 设置 `last_cf_count_needs_save_ = true`
3. 在下次 `process_energy_persistence` 中触发标志位逻辑
4. 执行保存和更新操作

## 注意事项

### 1. 逻辑变化影响
- 新公式会导致 `persistent_cf_count` 的含义发生根本性变化
- 需要确保这种变化符合实际的业务需求

### 2. 数据兼容性
- 修改后的数据格式可能与现有存储不兼容
- 建议更新hash值或版本号以避免冲突

### 3. 测试验证
- 需要充分测试新逻辑在各种场景下的表现
- 特别关注芯片重启和数据持久化的正确性

### 4. 日志调试
- 增加详细的日志输出以便调试新逻辑
- 记录关键变量的变化过程

### 第七步：修改电量传感器发布逻辑

**重要修正：** 电量传感器应该发布持久化CF_count转换的数据，而不是原始硬件数据。

#### 7.1 修改各通道电量传感器
```cpp
// 在 publish_all_sensors 方法中
if (energy_sensors_[i]) {
  // 使用持久化CF_count而不是原始硬件值
  if (energy_persistence_enabled_) {
    float value = persistent_cf_count_[i] / Ke;  // 直接转换持久化CF_count
    energy_sensors_[i]->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "通道%d电量: 持久化CF_count=%u, 计算值=%.6f kWh", i+1, persistent_cf_count_[i], value);
  } else {
    // 持久化禁用时使用原始硬件值
    float value = convert_raw_to_value(BL0906_CF_CNT[i], data.channels[i].energy_raw);
    energy_sensors_[i]->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "通道%d电量: 原始值=%u, 计算值=%.2f", i+1, data.channels[i].energy_raw, value);
  }
}
```

#### 7.2 修改总和电量传感器
```cpp
if (energy_sum_sensor_) {
  // 使用持久化CF_count而不是原始硬件值
  if (energy_persistence_enabled_) {
    float value = persistent_cf_count_[6] / Ke_sum;  // 使用持久化总和CF_count
    energy_sum_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "总电量: 持久化CF_count=%u, 计算值=%.6f kWh", persistent_cf_count_[6], value);
  } else {
    // 持久化禁用时使用原始硬件值
    float value = convert_raw_to_value(BL0906_CF_SUM_CNT, data.energy_sum_raw);
    energy_sum_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "总电量: 原始值=%u, 计算值=%.2f", data.energy_sum_raw, value);
  }
}
```

**说明：** 这个修改确保了：
- 电量传感器发布的是经过持久化处理的真实累计电量
- 即使芯片重启，电量数据也不会回零
- 持久化禁用时仍然可以正常显示硬件原始数据

## 实施顺序

1. **[已完成]** 修改电量传感器发布逻辑（优先处理）
2. **[已完成]** 添加新的成员变量 `saved_total_cf_`
3. **[已完成]** 修改数据更新公式
4. **[已完成]** 实现新的触发逻辑（使用内联计算）
5. **[已完成]** 修改保存方法
6. **[已完成]** 更新初始化逻辑
7. 进行全面测试和验证

## ✅ 修改完成总结

所有核心修改已完成，主要变化包括：

### 🔧 数据更新公式修改
- **旧逻辑**: `persistent_cf_count_[i] += pulse_increment` (累加增量)
- **新逻辑**: `persistent_cf_count_[i] = current_count + last_cf_count_[i]` (直接相加)

### ⚡ 触发机制重构
- **标志位触发**: 当 `last_cf_count_needs_save_ = true` 时立即保存
- **总和校验触发**: 6通道CF总和差值≥1000时触发保存
- **移除了**: 基于脉冲增量的自动保存逻辑

### 💾 保存逻辑简化
- 移除了复杂的变化检测逻辑
- 每次调用都直接执行保存操作
- 保持了数据完整性校验

### 📊 传感器数据修正
- 电量传感器现在发布持久化CF_count转换的数据
- 确保断电重启后电量数据连续性

### 🎯 代码优化
- 使用内联计算替代辅助方法
- 代码更简洁，逻辑更清晰
- 满足"力求代码简洁"的要求

这个修改计划确保了平滑过渡和最小化风险，同时满足用户提出的所有要求。 