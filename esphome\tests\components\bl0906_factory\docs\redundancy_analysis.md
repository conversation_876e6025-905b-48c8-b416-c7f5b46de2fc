# BL0906 Factory组件代码冗余分析报告

## 一、状态机通道读取逻辑冗余
**文件**: bl0906_factory.cpp (201-400行)
**冗余描述**: 状态机中READ_CHANNEL_1至READ_CHANNEL_6的实现逻辑高度重复，每个通道的电流、功率和能量读取代码结构完全一致，仅通道索引和寄存器地址不同。
**示例代码片段**:
```cpp
case State::READ_CHANNEL_1:
  // 直接调用统一读取函数读取通道1数据
  {
    bool success = false;
    int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[0], &success);
    current_data_.channels[0].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
    // ... 功率和能量读取代码
  }
  this->current_state_ = State::READ_CHANNEL_2;
  break;

case State::READ_CHANNEL_2:
  // 直接调用统一读取函数读取通道2数据
  {
    bool success = false;
    int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[1], &success);
    current_data_.channels[1].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
    // ... 功率和能量读取代码
  }
  this->current_state_ = State::READ_CHANNEL_3;
  break;
```
**精简建议**: 使用循环结构遍历6个通道，通过索引访问通道数据和寄存器地址，减少重复代码量约80%。

## 二、通信适配器实现冗余
### 2.1 UART与SPI适配器功能冗余
**文件**: uart_communication_adapter.cpp 和 spi_communication_adapter.cpp
**冗余描述**: 两个通信适配器实现了几乎相同的功能接口（read_register、send_write_command等），且内部校验和计算、数据解析逻辑高度相似，但分别维护两套独立代码。

### 2.2 UART适配器寄存器读取冗余
**文件**: uart_communication_adapter.cpp (201-400行)
**冗余描述**: send_read_command_and_receive函数中，16位和24位寄存器的数据处理逻辑存在重复的符号扩展和数据组装代码。

## 三、能量统计传感器更新冗余
**文件**: energy_statistics_manager.cpp (201-400行)
**冗余描述**: update_sensors()函数中，对昨日/今日/本周/本月/本年的电量传感器更新逻辑完全重复，仅传感器类型和周期参数不同。
**示例代码片段**:
```cpp
// 昨日电量
if (yesterday_energy_sensors_[channel]) {
  float energy = (channel < CHANNEL_COUNT) ? 
    calculate_energy_for_period(channel, EnergyPeriod::YESTERDAY) :
    calculate_total_energy_for_period(EnergyPeriod::YESTERDAY);
  yesterday_energy_sensors_[channel]->publish_state(energy);
}

// 今日电量
if (today_energy_sensors_[channel]) {
  float energy = (channel < CHANNEL_COUNT) ? 
    calculate_energy_for_period(channel, EnergyPeriod::TODAY) :
    calculate_total_energy_for_period(EnergyPeriod::TODAY);
  today_energy_sensors_[channel]->publish_state(energy);
}
```
**精简建议**: 创建通用的传感器更新函数，通过周期类型和传感器数组作为参数，循环处理所有周期类型，减少重复代码约60%。

## 四、时间周期判断逻辑冗余
**文件**: energy_statistics_manager.cpp (201-400行)
**冗余描述**: is_new_day()、is_new_week()、is_new_month()、is_new_year()等时间判断函数结构相似，均通过比较时间戳字段实现周期切换检测。

## 五、错误处理与日志记录冗余
**文件**: 所有通信相关文件
**冗余描述**: 各文件中错误处理（如校验和错误、超时错误）和日志记录代码模式一致，但分散在不同函数中，未形成统一的错误处理机制。