#pragma once

#include "esphome/core/component.h"
#include "esphome/components/sensor/sensor.h"

// 芯片参数条件编译配置（必须在最前面）
#include "bl0906_chip_params.h"

// 新的通信适配器系统
#include "communication_adapter_interface.h"
#include <memory>
#include "bl0906_calibration.h"
#include "bl0906_chip_params.h"
#include "esphome/components/number/number.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "esphome/core/defines.h"
#include "esphome/core/log.h"
#include "esphome/core/time.h"
#include "esphome/components/time/real_time_clock.h"
#include "esphome/components/sensor/sensor.h"
#include "esphome/core/preferences.h"
#include <map>
#include "bl0906_number.h"
#include "energy_statistics_manager.h"
#include <memory>

// 新的统一校准存储系统
#include "calibration_storage_interface.h"
#include "preference_calibration_storage.h"

// 条件包含I2C EEPROM存储
#ifdef USE_I2C_EEPROM_CALIBRATION
  #include "i2c_eeprom_calibration_storage.h"
  #include "esphome/components/i2c/i2c.h"
#endif

// 前向声明以避免编译错误
#ifndef USE_I2C_EEPROM_CALIBRATION
namespace esphome {
namespace bl0906_factory {
  enum class EEPROMType : uint8_t;
}
}
#endif

// 避免命名空间冲突
namespace esphome_time = esphome::time;

namespace esphome {
namespace bl0906_factory {

// 扩展传感器类型枚举（用于统计功能）
enum class StatisticsSensorType {
  YESTERDAY_ENERGY,        // 昨日电量（各通道）
  TODAY_ENERGY,           // 今日电量（各通道）
  WEEK_ENERGY,            // 本周电量（各通道）
  MONTH_ENERGY,           // 本月电量（各通道）
  YEAR_ENERGY,            // 本年电量（各通道）
  YESTERDAY_TOTAL_ENERGY, // 昨日总电量
  TODAY_TOTAL_ENERGY,     // 今日总电量
  WEEK_TOTAL_ENERGY,      // 本周总电量
  MONTH_TOTAL_ENERGY,     // 本月总电量
  YEAR_TOTAL_ENERGY       // 本年总电量
};

// 硬件资料
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/guide/BL0906%20APP%20Note_V1.02.pdf
// https://www.belling.com.cn/media/file_object/bel_product/BL0906/datasheet/BL0906_V1.02_cn.pdf

// 注意：字节顺序为大端模式(big endian)：数据的高位字节保存在内存的低地址中，而低位字节保存在内存的高地址中。（低-中-高）

// 前向声明类
class BL0906Factory;
class EnergyStatisticsManager;
using ActionCallbackFuncPtr = void (BL0906Factory::*)();

// 原始数据存储结构（使用条件编译的通道数）
struct RawSensorData {
  // 基础传感器原始数据（无符号）
  uint32_t temperature_raw;
  uint32_t frequency_raw;
  uint32_t voltage_raw;
  
  // 通道数据（编译时确定大小）
  struct ChannelData {
    uint32_t current_raw;    // 无符号
    int32_t power_raw;       // 有符号
    uint32_t energy_raw;     // 无符号
    bool is_valid;           // 数据有效性标志
  } channels[CHANNEL_COUNT];  // 编译时常量：BL0906=6, BL0910=10
  
  // 总和数据
  int32_t power_sum_raw;     // 有符号
  uint32_t energy_sum_raw;   // 无符号
  
  // 数据读取时间戳
  uint32_t timestamp;
  bool read_complete;
};

// 优化的电量持久化存储数据结构（基于CF_count）- 使用条件编译的数组大小
struct EnergyPersistenceData {
  // 核心持久化数据：只保留真正需要跨重启保存的数据
  uint32_t persistent_cf_count[ARRAY_SIZE];    // [0-(CHANNEL_COUNT-1)]为各通道，[CHANNEL_COUNT]为总和
  uint32_t last_cf_count[ARRAY_SIZE];          // [0-(CHANNEL_COUNT-1)]为各通道，[CHANNEL_COUNT]为总和
  uint32_t save_count;                         // 存储计数器 - 记录持久存储触发次数
  uint32_t checksum;                           // 数据校验和
  
  // 移除的字段说明：
  // - cf_count_initialized[ARRAY_SIZE]: 不需要持久化，使用硬件CF_count<100判断是否重启
  // - device_boot_time: 不需要持久化，重启后这个数据就归零了
  // - save_timestamp: 不需要持久化，重启后这个数据就归零了
  // - current_persistent_cf_count[ARRAY_SIZE]: 可以从BL0906Factory类中实时读取，不需要持久化
};

// 定义数据包结构体，用于UART通信
struct DataPacket {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
  uint8_t checksum;     // checksum
};

// 无符号BL0906 24位数据类型
struct ube24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  uint8_t h;            // high byte
};

// 带符号BL0906 24位数据类型
struct sbe24_t {
  uint8_t l;            // low byte
  uint8_t m;            // middle byte
  int8_t h;             // high byte, signed for proper sign extension
};

// 校准寄存器类型枚举
enum class CalibRegType {
  CHGN,     // 电流增益
  CHOS,     // 电流偏置
  RMSGN,    // 有效值增益
  RMSOS,    // 有效值偏置
  WATTGN,   // 功率增益
  WATTOS,   // 功率偏置
  CHGN_V,   // 电压增益
  CHOS_V    // 电压偏置
};



// 在BL0906Factory类声明之前声明BL0906Number类
class BL0906Number;

class BL0906Factory : public PollingComponent {
 public:

  static const char *const BL0906_FACTORY_ID; // 原BL0906_CALIB_ID

  enum class State {
    IDLE,
    READ_BASIC_SENSORS,      // 读取温度、频率、电压原始数据
    READ_CHANNELS,           // 读取所有通道数据（电流、功率、电量）- 重构：合并6个通道状态
    READ_TOTAL_DATA,         // 读取总功率和总电量
    CHECK_CHIP_RESTART,      // 检测芯片重启（基于完整数据集）
    PROCESS_PERSISTENCE,     // 处理持久化存储
    UPDATE_STATISTICS,       // 更新能量统计
    PUBLISH_SENSORS,         // 发布所有传感器数据
    HANDLE_ACTIONS          // 处理动作队列
  };

  // 现代化传感器管理 - 使用枚举和模板
  enum class SensorType {
    VOLTAGE,
    FREQUENCY,
    TEMPERATURE,
    CURRENT,
    POWER,
    ENERGY,
    POWER_SUM,
    ENERGY_SUM,
    TOTAL_ENERGY,      // 各通道累计电量
    TOTAL_ENERGY_SUM   // 总累计电量
  };


  // 统一的传感器设置接口
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0) {
    switch (type) {
      case SensorType::VOLTAGE:
        voltage_sensor_ = sensor;
        break;
      case SensorType::FREQUENCY:
        frequency_sensor_ = sensor;
        break;
      case SensorType::TEMPERATURE:
        temperature_sensor_ = sensor;
        break;
      case SensorType::CURRENT:
        if (is_valid_channel(channel)) {
          current_sensors_[channel] = sensor;
        }
        break;
      case SensorType::POWER:
        if (is_valid_channel(channel)) {
          power_sensors_[channel] = sensor;
        }
        break;
      case SensorType::ENERGY:
        if (is_valid_channel(channel)) {
          energy_sensors_[channel] = sensor;
        }
        break;
      case SensorType::POWER_SUM:
        power_sum_sensor_ = sensor;
        break;
      case SensorType::ENERGY_SUM:
        energy_sum_sensor_ = sensor;
        break;
      case SensorType::TOTAL_ENERGY:
        if (is_valid_channel(channel)) {
          total_energy_sensors_[channel] = sensor;
        }
        break;
      case SensorType::TOTAL_ENERGY_SUM:
        total_energy_sum_sensor_ = sensor;
        break;
    }
  }

  // 获取传感器的统一接口
  sensor::Sensor* get_sensor(SensorType type, int channel = 0) const {
    switch (type) {
      case SensorType::VOLTAGE: return voltage_sensor_;
      case SensorType::FREQUENCY: return frequency_sensor_;
      case SensorType::TEMPERATURE: return temperature_sensor_;
      case SensorType::CURRENT:
        return is_valid_channel(channel) ? current_sensors_[channel] : nullptr;
      case SensorType::POWER:
        return is_valid_channel(channel) ? power_sensors_[channel] : nullptr;
      case SensorType::ENERGY:
        return is_valid_channel(channel) ? energy_sensors_[channel] : nullptr;
      case SensorType::POWER_SUM: return power_sum_sensor_;
      case SensorType::ENERGY_SUM: return energy_sum_sensor_;
      case SensorType::TOTAL_ENERGY:
        return is_valid_channel(channel) ? total_energy_sensors_[channel] : nullptr;
      case SensorType::TOTAL_ENERGY_SUM:
        return total_energy_sum_sensor_;
      default: return nullptr;
    }
  }

  // 批量设置通道传感器
  void set_channel_sensors(SensorType type, const std::vector<sensor::Sensor*>& sensors) {
    for (size_t i = 0; i < sensors.size() && i < MAX_CHANNELS; ++i) {
      set_sensor(type, sensors[i], i);
    }
  }

  // 获取所有通道传感器
  std::vector<sensor::Sensor*> get_channel_sensors(SensorType type) const {
    std::vector<sensor::Sensor*> sensors;
    for (int i = 0; i < MAX_CHANNELS; ++i) {
      sensors.push_back(get_sensor(type, i));
    }
    return sensors;
  }

  // 现代化校准Number组件管理
  enum class CalibNumberType {
    CHGN,      // 电流增益
    CHOS,      // 电流偏置
    RMSGN,     // 有效值增益
    RMSOS,     // 有效值偏置
    WATTGN,    // 功率增益
    WATTOS,    // 功率偏置
    CHGN_V,    // 电压增益
    CHOS_V     // 电压偏置
  };

  // 频率适配模式枚举
  enum class FreqAdaptMode {
    OFF,    // 禁用频率适配，使用芯片默认50Hz
    AUTO,   // 自动检测频率并设置
    HZ60    // 强制设置为60Hz，不检测
  };

  // 统一的校准Number设置接口
  void set_calib_number(CalibNumberType type, number::Number *num, int channel = -1) {
    // 使用map存储，key为类型和通道的组合
    uint32_t key = static_cast<uint32_t>(type) << 8 | (channel & 0xFF);
    calib_numbers_map_[key] = num;

    // 同时添加到向量中以保持兼容性，并设置父指针
    if (num) {
      BL0906Number* bl0906_num = static_cast<BL0906Number*>(num);
      bl0906_num->set_parent(this);  // 设置父指针
      calib_numbers_.push_back(bl0906_num);
    }
  }

  // 获取校准Number组件
  number::Number* get_calib_number(CalibNumberType type, int channel = -1) const {
    uint32_t key = static_cast<uint32_t>(type) << 8 | (channel & 0xFF);
    auto it = calib_numbers_map_.find(key);
    return (it != calib_numbers_map_.end()) ? it->second : nullptr;
  }

  // 简化的校准寄存器初始值设置方法，使用一个通用方法替代多个单独的方法
  void set_initial_calib_value(CalibRegType type, int channel, int16_t value) {
    uint8_t reg_addr = get_register_address(type, channel);
    if (reg_addr != 0) {
      initial_calibration_values_[reg_addr] = value;
    }
  }


  // 获取校准寄存器地址的统一方法
  uint8_t get_register_address(CalibRegType type, int channel) {
    // 通道范围检查
    if (channel < 0 || channel > 6) return 0;

    // 使用具体寄存器常量获取地址
    switch(type) {
      case CalibRegType::CHGN:
        if (channel == 0) return BL0906_CHGN_V;
        switch(channel) {
          case 1: return BL0906_CHGN_1;
          case 2: return BL0906_CHGN_2;
          case 3: return BL0906_CHGN_3;
          case 4: return BL0906_CHGN_4;
          case 5: return BL0906_CHGN_5;
          case 6: return BL0906_CHGN_6;
          default: return 0;
        }

      case CalibRegType::CHOS:
        if (channel == 0) return BL0906_CHOS_V;
        switch(channel) {
          case 1: return BL0906_CHOS_1;
          case 2: return BL0906_CHOS_2;
          case 3: return BL0906_CHOS_3;
          case 4: return BL0906_CHOS_4;
          case 5: return BL0906_CHOS_5;
          case 6: return BL0906_CHOS_6;
          default: return 0;
        }

      case CalibRegType::RMSGN:
        if (channel == 0) return BL0906_RMSGN_V;
        switch(channel) {
          case 1: return BL0906_RMSGN_1;
          case 2: return BL0906_RMSGN_2;
          case 3: return BL0906_RMSGN_3;
          case 4: return BL0906_RMSGN_4;
          case 5: return BL0906_RMSGN_5;
          case 6: return BL0906_RMSGN_6;
          default: return 0;
        }

      case CalibRegType::RMSOS:
        if (channel == 0) return BL0906_RMSOS_V;
        switch(channel) {
          case 1: return BL0906_RMSOS_1;
          case 2: return BL0906_RMSOS_2;
          case 3: return BL0906_RMSOS_3;
          case 4: return BL0906_RMSOS_4;
          case 5: return BL0906_RMSOS_5;
          case 6: return BL0906_RMSOS_6;
          default: return 0;
        }

      case CalibRegType::WATTGN:
        if (channel == 0) return 0; // 电压通道没有WATTGN
        switch(channel) {
          case 1: return BL0906_WATTGN_1;
          case 2: return BL0906_WATTGN_2;
          case 3: return BL0906_WATTGN_3;
          case 4: return BL0906_WATTGN_4;
          case 5: return BL0906_WATTGN_5;
          case 6: return BL0906_WATTGN_6;
          default: return 0;
        }

      case CalibRegType::WATTOS:
        if (channel == 0) return 0; // 电压通道没有WATTOS
        switch(channel) {
          case 1: return BL0906_WATTOS_1;
          case 2: return BL0906_WATTOS_2;
          case 3: return BL0906_WATTOS_3;
          case 4: return BL0906_WATTOS_4;
          case 5: return BL0906_WATTOS_5;
          case 6: return BL0906_WATTOS_6;
          default: return 0;
        }

      case CalibRegType::CHGN_V:
        return BL0906_CHGN_V;

      case CalibRegType::CHOS_V:
        return BL0906_CHOS_V;

      default:
        return 0;
    }
  }

  void loop() override;
  void update();
  void setup() override;
  BL0906Factory();
  
  // 静态内联函数，编译时常量
  static constexpr int get_max_channels() { return MAX_CHANNELS; }
  static constexpr const char* get_chip_name() { return CHIP_MODEL_NAME; }
  
  // ========== 通信适配器接口 ==========
  
  /**
   * 设置通信适配器
   * @param adapter 通信适配器智能指针
   */
  void set_communication_adapter(std::unique_ptr<CommunicationAdapterInterface> adapter);
  
  /**
   * 获取通信适配器
   * @return 通信适配器指针
   */
  CommunicationAdapterInterface* get_communication_adapter() const;
  
  // 对外公开读写寄存器接口(供Number组件使用) - 现在通过适配器实现
  int32_t read_register_value(uint8_t address);
  bool write_register_value(uint8_t address, int16_t value);

  // 统一的数据转换函数（根据寄存器地址和原始值转换为实际值）
  // 这是唯一的数据转换函数，所有数据转换都通过此函数
  float convert_raw_to_value(uint8_t address, int32_t raw_value);

  // 寄存器类型判断函数已移至 bl0906_registers.h 作为内联函数
  // 这里保留声明以兼容旧代码，但实际使用全局内联函数

  // 添加方法用于注册校准数字组件
  void register_calib_number(BL0906Number *number);

  // 添加刷新所有校准数字组件的方法
  void refresh_all_calib_numbers();

  // 添加写保护相关方法
  bool turn_off_write_protect();

  // RMSOS自动计算函数（简化版）
  void calculate_and_write_rmsos_all_channels();

  // 批量修改所有CHGN值的方法（用于template button调用）
  void update_all_chgn_values_from_sensor();
  
  // 设置用于批量修改CHGN值的参考传感器
  void set_chgn_reference_sensor(sensor::Sensor *sensor) { chgn_reference_sensor_ = sensor; }
  
  // 批量将所有CHGN值清零的方法（用于template button调用）
  void reset_all_chgn_values_to_zero();
  
  // 批量将所有RMSOS值清零的方法（用于template button调用）
  void reset_all_rmsos_values_to_zero();

  // 统一的寄存器读取函数（现在通过适配器实现）
  // 这是唯一的数据读取函数，所有数据读取都通过此函数
  // success参数用于区分读取失败和寄存器值确实为0的情况
  int32_t send_read_command_and_receive(uint8_t address, bool* success);

  // 应用校准值的方法
  void apply_calibration_values();

  // 电量持久化存储相关方法（基于CF_count）
  void setup_energy_persistence();
  void save_energy_data();
  void load_energy_data();
  void reset_energy_data();
  void set_energy_persistence_enabled(bool enabled);
  void check_and_sync_hardware_cf_count();  // 检查并同步硬件CF_count
  void detect_and_repair_corrupted_data();  // 检测并修复损坏的持久化数据
  
  // 存储计数相关方法
  uint32_t get_save_count() const { return current_save_count_; }  // 获取当前存储计数
  void reset_save_count() { current_save_count_ = 0; }             // 重置存储计数
  
  // 统计功能相关方法
  void set_energy_statistics_enabled(bool enabled);
  void set_time_component(esphome_time::RealTimeClock *time_comp);
  void set_statistics_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel = 0);
  void set_statistics_update_interval(uint32_t update_interval_ms);  // 设置统计传感器更新间隔
  
  // 获取持久化CF_count相关方法
  float calculate_total_energy_from_cf_count(int channel) const;
  
  // 公开方法供外部调用
  void force_save_energy_data() { save_energy_data(); }
  void reload_energy_data() { load_energy_data(); }

  // 添加诊断方法
  void diagnose_energy_persistence();  // 移到cpp文件实现

  // 操作队列相关方法 - 移到public以供lambda调用
  size_t enqueue_action_(ActionCallbackFuncPtr function);

  // 校准存储相关方法
#ifdef BL0906_CALIBRATION_MODE
  // 批量保存所有校准值到存储（仅手动调用，供Button使用）
  void save_all_calibration_to_flash();
#endif
  
  // 从Flash加载校准值（通用方法）
  void load_calibration_from_flash();
  
  // 实例ID相关方法（用于多实例EEPROM存储）
  void set_instance_id(uint32_t id);
  uint32_t get_instance_id() const;
  uint32_t generate_instance_id() const;

  // 创建与当前配置匹配的校准存储实例（供外部使用）
  std::unique_ptr<CalibrationStorageInterface> create_storage_instance();

  // 封装的校准数据操作方法（供YAML按钮调用）
  void read_and_display_calibration_data();
  void show_all_instances_calibration_data();
  void show_storage_status();
  void clear_calibration_storage();
  void force_recover_calibration_data();  // 新增：强制恢复校准数据
  void diagnose_nvs_storage();  // 新增：NVS存储诊断

  // 新的存储配置接口
  void set_storage_type(const std::string& type) { storage_type_ = type; }
#ifdef USE_I2C_EEPROM_CALIBRATION
  void set_eeprom_type(EEPROMType type) { eeprom_type_ = type; }
  void set_i2c_parent(i2c::I2CBus *parent) { i2c_parent_ = parent; }
  void set_i2c_address(uint8_t address) { i2c_address_ = address; }
#endif

  // 频率适配配置方法
  void set_freq_adapt_mode(FreqAdaptMode mode) { freq_adapt_mode_ = mode; }

 protected:
  // ========== 通信适配器 ==========
  std::unique_ptr<CommunicationAdapterInterface> comm_adapter_;
  
  // 状态相关变量
  State current_state_{State::IDLE};
  int current_channel_{0};

  // 原始数据存储
  RawSensorData current_data_;        // 当前周期的原始数据
  bool data_collection_complete_;     // 数据收集完成标志
  uint32_t data_read_start_time_;     // 数据读取开始时间

  // 操作队列相关方法
  void handle_actions_();
  std::vector<ActionCallbackFuncPtr> action_queue_{};

  // 存储YAML配置中的初始校准值
  std::map<uint8_t, int16_t> initial_calibration_values_{};

  // 基础传感器
  sensor::Sensor *voltage_sensor_{nullptr};
  sensor::Sensor *frequency_sensor_{nullptr};
  sensor::Sensor *temperature_sensor_{nullptr};
  sensor::Sensor *power_sum_sensor_{nullptr};
  sensor::Sensor *energy_sum_sensor_{nullptr};
  sensor::Sensor *total_energy_sum_sensor_{nullptr};

  // 通道传感器数组（编译时确定大小）
  sensor::Sensor *current_sensors_[CHANNEL_COUNT]{nullptr};
  sensor::Sensor *power_sensors_[CHANNEL_COUNT]{nullptr};
  sensor::Sensor *energy_sensors_[CHANNEL_COUNT]{nullptr};
  sensor::Sensor *total_energy_sensors_[CHANNEL_COUNT]{nullptr};
  
  // 用于批量修改CHGN值的参考传感器
  sensor::Sensor *chgn_reference_sensor_{nullptr};

  // 辅助函数
  // 移除条件编译的方法，现在由适配器处理
  bool is_valid_channel(int channel) const;

  // 数据处理流水线方法
  void detect_chip_restart(const RawSensorData& data);
  void process_energy_persistence(const RawSensorData& data);
  void update_energy_statistics(const RawSensorData& data);
  void publish_all_sensors(const RawSensorData& data);

  // 新的一次性读取方法（重构后）
  bool read_all_channels_data();
  bool read_single_channel_data(int channel);

  // 校准寄存器读取方法
  void read_calib_register(CalibNumberType type, int channel = -1);

 public:
  static BL0906Factory *bl0906_instance;  // 添加静态实例指针
  static void set_instance(BL0906Factory *instance) {
    bl0906_instance = instance;
  }

  // 添加一个新的方法用于初始化互斥锁
  void init_mutex() {
    if (mutex_ == nullptr) {
      mutex_ = xSemaphoreCreateMutex();
      if (mutex_ == nullptr) {
        ESP_LOGE("bl0906_factory", "Failed to create mutex");
      }
    }
  }

  // 获取互斥锁
  bool lock(int timeout_ms = 100) {
    if (mutex_ == nullptr) {
      ESP_LOGW("bl0906_factory", "Mutex not initialized");
      return false;
    }
    return xSemaphoreTake(mutex_, timeout_ms / portTICK_PERIOD_MS) == pdTRUE;
  }

  // 释放互斥锁
  void unlock() {
    if (mutex_ != nullptr) {
      xSemaphoreGive(mutex_);
    }
  }

  // 现代化成员变量
  std::vector<BL0906Number *> calib_numbers_;
  std::map<uint32_t, number::Number*> calib_numbers_map_;  // 新的校准Number存储

  // 互斥锁
  SemaphoreHandle_t mutex_{nullptr};

  // 电量持久化存储相关成员变量（优化版本 - 减少Flash写入但保留必要数据）
  ESPPreferenceObject energy_pref_;                    // preferences对象
  uint32_t persistent_cf_count_[7]{0};                // [0-5]为各通道，[6]为总和 - 软件维护的累计CF_count
  uint32_t last_cf_count_[7]{0};                      // [0-5]为各通道，[6]为总和 - 上次读取的硬件CF_count
  uint32_t last_save_time_{0};                        // 上次保存时间
  uint32_t current_save_count_{0};                    // 当前存储计数器 - 记录持久存储触发次数
  bool energy_persistence_enabled_{true};             // 是否启用电量持久化
  bool last_cf_count_needs_save_{false};              // 标记last_cf_count是否需要保存（只在BL0906重启时为true）
  
  // 电量统计管理器
  std::unique_ptr<EnergyStatisticsManager> energy_stats_manager_;
  bool energy_statistics_enabled_{true};             // 是否启用电量统计功能
  
  // 芯片重启检测相关成员变量
  bool chip_restart_detected_{false};                // 标记是否已检测到芯片重启
  uint32_t last_restart_detection_time_{0};          // 上次检测到重启的时间（用于调试和超时机制）
  uint32_t chip_restart_count_{0};                   // 芯片重启次数计数（用于系统监控）

  bool cf_count_initialized_[7]{false};              // 脉冲计数是否已初始化（7个元素：6通道+1总和）
  
  // 新增变量：记录上次保存时的6通道CF总和
  uint32_t saved_total_cf_{0};
  
  // 新的统一校准存储系统
private:
  // 实例ID（用于多实例存储）
  uint32_t instance_id_{0};
  
  // 存储配置
  std::string storage_type_{"preference"};  // "preference" 或 "eeprom"
#ifdef USE_I2C_EEPROM_CALIBRATION
  EEPROMType eeprom_type_{EEPROMType::TYPE_24C02};
  i2c::I2CBus *i2c_parent_{nullptr};
  uint8_t i2c_address_{0x50};
#endif
  
  // 统一的校准存储接口
  std::unique_ptr<CalibrationStorageInterface> calibration_storage_;
  
  // 核心功能
  bool init_calibration_storage();
  bool load_calibration_data();
  bool save_calibration_data();

  // 频率适配模式
  FreqAdaptMode freq_adapt_mode_{FreqAdaptMode::OFF};
  bool freq_adapted_{false};                            // 频率适配完成标志
  
  // 硬编码的检测参数
  static constexpr float FREQ_DETECT_THRESHOLD_LOW = 55.0f;   // 60Hz检测下限
  static constexpr float FREQ_DETECT_THRESHOLD_HIGH = 65.0f;  // 60Hz检测上限
  static constexpr uint8_t FREQ_DETECTION_SAMPLES = 3;        // 频率检测采样次数

 private:
  // 频率检测和适配方法
  float detect_grid_frequency();
  bool set_ac_frequency_mode(bool is_60hz);
  uint32_t read_mode2_register();
  bool write_mode2_register(uint32_t value);
  
  // 24位寄存器写入方法
  bool write_register_24bit(uint8_t address, uint32_t value);
  
  // is_24bit_register 函数已移至 bl0906_registers.h 作为内联函数
};

// 合并BL0906FactoryNumber和BL0906Number为BL0906Number，保留所有接口和功能
// 已移至bl0906_number.h

}  // namespace bl0906_factory
}  // namespace esphome
