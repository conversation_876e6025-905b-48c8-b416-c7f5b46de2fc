bl0906_factory:
    - id: sensor_bl0906
      communication: uart
      uart_id: uart_bus1
      update_interval: 4s
      instance_id: 0x906B0001
      calibration_mode: true
      calibration:
        enabled: true
        storage_type: preference
    - id: sensor_bl0910
      chip_model: bl0910
      communication: uart
      uart_id: uart_bus2
      update_interval: 4s
      instance_id: 0x910B0001
      calibration_mode: true
      calibration:
        enabled: true
        storage_type: preference

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    # 全局传感器
    frequency:
      name: 'BL0906 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'BL0906 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'BL0906 Voltage'
      icon: "mdi:lightning-bolt-outline"
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "6-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 2
      unit_of_measurement: kWh

    # 总电量统计传感器
    today_total_energy:
      name: "Today Total Energy"
      icon: "mdi:calendar-today"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    yesterday_total_energy:
      name: "Yesterday Total Energy"
      icon: "mdi:calendar-minus"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    week_total_energy:
      name: "Week Total Energy"
      icon: "mdi:calendar-week"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    month_total_energy:
      name: "Month Total Energy"
      icon: "mdi:calendar-month"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    year_total_energy:
      name: "Year Total Energy"
      icon: "mdi:calendar"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats

    # 通道1配置
    ch1:
      current:
        name: "${ch11} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch11} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch11_energy
        name: "${ch11} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch11} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch11} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch11} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch11} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch11} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道2配置
    ch2:
      current:
        name: "${ch12} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch12} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch12_energy
        name: "${ch12} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
      today_energy:
        name: "${ch12} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch12} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch12} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch12} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch12} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道3配置
    ch3:
      current:
        name: "${ch13} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch13} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch13_energy
        name: "${ch13} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
      
      today_energy:
        name: "${ch13} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch13} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch13} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch13} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch13} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道4配置
    ch4:
      current:
        name: "${ch14} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch14} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch14_energy
        name: "${ch14} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
      
      today_energy:
        name: "${ch14} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch14} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch14} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch14} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch14} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道5配置
    ch5:
      current:
        name: "${ch15} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch15} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch15_energy
        name: "${ch15} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
      
      today_energy:
        name: "${ch15} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch15} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch15} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch15} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch15} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道6配置
    ch6:
      current:
        name: "${ch16} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch16} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch16_energy
        name: "${ch16} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
      
      today_energy:
        name: "${ch16} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch16} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch16} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch16} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch16} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats


  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0910
    
    # 全局传感器
    frequency:
      name: 'bl0910 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'bl0910 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'bl0910 Voltage'
      icon: "mdi:lightning-bolt-outline"
    
    # 总和传感器
    power_sum:
      name: "10-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "10-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 2
      unit_of_measurement: kWh

    # 总电量统计传感器
    today_total_energy:
      name: "Today Total Energy"
      icon: "mdi:calendar-today"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    yesterday_total_energy:
      name: "Yesterday Total Energy"
      icon: "mdi:calendar-minus"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    week_total_energy:
      name: "Week Total Energy"
      icon: "mdi:calendar-week"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    month_total_energy:
      name: "Month Total Energy"
      icon: "mdi:calendar-month"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    year_total_energy:
      name: "Year Total Energy"
      icon: "mdi:calendar"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats

    # 通道1配置
    ch1:
      current:
        name: "${ch1} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch1} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch1_energy
        name: "${ch1} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch1} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch1} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch1} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch1} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch1} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道2配置
    ch2:
      current:
        name: "${ch2} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch2} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch2_energy
        name: "${ch2} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch2} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch2} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch2} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch2} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch2} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道3配置
    ch3:
      current:
        name: "${ch3} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch3} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch3_energy
        name: "${ch3} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch3} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch3} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch3} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch3} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch3} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道4配置
    ch4:
      current:
        name: "${ch4} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch4} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch4_energy
        name: "${ch4} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch4} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch4} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch4} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch4} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch4} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道5配置
    ch5:
      current:
        name: "${ch5} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch5} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch5_energy
        name: "${ch5} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch5} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch5} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch5} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch5} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch5} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道6配置
    ch6:
      current:
        name: "${ch6} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch6} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch6_energy
        name: "${ch6} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch6} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch6} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch6} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch6} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch6} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道7配置 (BL0910扩展通道)
    ch7:
      current:
        name: "${ch7} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch7} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch7_energy
        name: "${ch7} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch7} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch7} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch7} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch7} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch7} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道8配置 (BL0910扩展通道)
    ch8:
      current:
        name: "${ch8} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch8} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch8_energy
        name: "${ch8} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch8} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch8} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch8} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch8} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch8} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道9配置 (BL0910扩展通道)
    ch9:
      current:
        name: "${ch9} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch9} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch9_energy
        name: "${ch9} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch9} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch9} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch9} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch9} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch9} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道10配置 (BL0910扩展通道)
    ch10:
      current:
        name: "${ch10} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch10} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch10_energy
        name: "${ch10} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing
      today_energy:
        name: "${ch10} Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "${ch10} Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "${ch10} Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "${ch10} Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "${ch10} Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
  # 系统状态监控传感器
  - platform: template
    name: "Device Uptime"
    unit_of_measurement: "s"
    accuracy_decimals: 0
    lambda: |-
      return millis() / 1000.0;
    update_interval: 60s

  - platform: template
    name: "Free Heap"
    unit_of_measurement: "bytes"
    accuracy_decimals: 0
    lambda: |-
      return esp_get_free_heap_size();
    update_interval: 60s

button:
  - platform: template
    name: "refresh all calib numbers 6ch"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy 6ch"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0906)) {
            id(sensor_bl0906)->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");
            }
  - platform: template
    name: "detect and repair energy data 6ch"
    on_press:
      - lambda: |-
          if (id(sensor_bl0906)) {
            id(sensor_bl0906)->detect_and_repair_corrupted_data();
          }
  - platform: template
    name: "Force Save Energy Data 6ch"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0906)) {
            id(sensor_bl0906)->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");
            }

  - platform: template
    name: "Reload Energy Data 6ch"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence 6ch"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0906)) {
            id(sensor_bl0906)->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
            }

  - platform: template
    name: "Save Calibration Data to Flash 6ch"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
                
  - platform: template  
    name: "Verify Calibration Data 6ch"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
        }
  - platform: template
    name: "Read Calibration Data from Flash 6ch"
    icon: "mdi:database-search"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->read_and_display_calibration_data();
            }
  - platform: template
    name: "Show All Instance Calibration Data 6ch"
    icon: "mdi:database-outline"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("button", "显示6ch BL0906实例校准数据 (ID: 0x%08X)", id(sensor_bl0906)->get_instance_id());
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->show_all_instances_calibration_data();
            }

  - platform: template
    name: "Clear Storage (Fix Full Storage) 6ch"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->clear_calibration_storage();
            }

  - platform: template
    name: "Show Storage Status 6ch"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("button", "=== 6ch BL0906存储状态诊断 ===");
            ESP_LOGI("button", "实例ID: 0x%08X", id(sensor_bl0906)->get_instance_id());
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->show_storage_status();
            }
  - platform: template
    name: "CALCULATE RMSOS 6ch"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);
  - platform: template
    name: "CALCULATE RMSOS 10ch"
    on_press:
      - lambda: |-
          auto bl0910 = id(sensor_bl0910);
          bl0910->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);
  # 批量设置所有CHGN值
  - platform: template
    name: "update all chgn values 6ch"
    id: update_all_chgn_button
    on_press:
      - lambda: |-
          // 调用BL0906Factory的批量修改方法
          id(sensor_bl0906)->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "CHGN 0 6ch"
    on_press:
      - lambda: id(sensor_bl0906)->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
  # 批量清零RMSOS值
  - platform: template
    name: "RMSOS 0 6ch"
    on_press:
      - lambda: id(sensor_bl0906)->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate

  - platform: template
    name: "refresh all calib numbers 10ch"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            bl0910->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy 10ch"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0910)) {
            id(sensor_bl0910)->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");
            }
            
  - platform: template
    name: "detect and repair energy data 10ch"
    on_press:
      - lambda: |-
          if (id(sensor_bl0910)) {
            id(sensor_bl0910)->detect_and_repair_corrupted_data();
          }
          
  - platform: template
    name: "Force Save Energy Data 10ch"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0910)) {
            id(sensor_bl0910)->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");
            }

  - platform: template
    name: "Reload Energy Data 10ch"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            bl0910->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence 10ch"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            if (id(sensor_bl0910)) {
            id(sensor_bl0910)->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
            }

  - platform: template
    name: "Save Calibration Data to Flash 10ch"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0910 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
        if (bl0910 != nullptr) {
          bl0910->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
                
  - platform: template  
    name: "Verify Calibration Data 10ch"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0910 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
        if (bl0910 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0910->refresh_all_calib_numbers();
        }
        
  - platform: template
    name: "Read Calibration Data from Flash 10ch"
    icon: "mdi:database-search"
    on_press:
      then:
        - lambda: |-
            auto* bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            if (bl0910 != nullptr) {
              bl0910->read_and_display_calibration_data();
            }
            
  - platform: template
    name: "Show All Instance Calibration Data 10ch"
    icon: "mdi:database-outline"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("button", "显示10ch BL0910实例校准数据 (ID: 0x%08X)", id(sensor_bl0910)->get_instance_id());
            auto* bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            if (bl0910 != nullptr) {
              bl0910->show_all_instances_calibration_data();
            }

  - platform: template
    name: "Clear Storage (Fix Full Storage) 10ch"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            if (bl0910 != nullptr) {
              bl0910->clear_calibration_storage();
            }

  - platform: template
    name: "Show Storage Status 10ch"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("button", "=== 10ch BL0910存储状态诊断 ===");
            ESP_LOGI("button", "实例ID: 0x%08X", id(sensor_bl0910)->get_instance_id());
            auto* bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
            if (bl0910 != nullptr) {
              bl0910->show_storage_status();
            }

  # 批量设置所有CHGN值
  - platform: template
    name: "update all chgn values 10ch"
    id: update_all_chgn_button_10ch
    on_press:
      - lambda: |-
          // 调用BL0906Factory的批量修改方法
          id(sensor_bl0910)->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
      
  - platform: template
    name: "CHGN 0 10ch"
    on_press:
      - lambda: id(sensor_bl0910)->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
      
  # 批量清零RMSOS值
  - platform: template
    name: "RMSOS 0 10ch"
    on_press:
      - lambda: id(sensor_bl0910)->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate

number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH_11 Current Gain"
      id: chgn_11_adjust
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_2:
      name: "CH_12 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_3:
      name: "CH_13 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_4:
      name: "CH_14 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_5:
      name: "CH_15 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_6:
      name: "CH_16 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_v_decimal:
      name: "Voltage Gain 6ch"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: power

    chos_decimal_1:
      name: "CH_11 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_2:
      name: "CH_12 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_3:
      name: "CH_13 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_4:
      name: "CH_14 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_5:
      name: "CH_15 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_6:
      name: "CH_16 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_v_decimal:
      name: "Voltage Offset 6ch"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: power
    # 添加十进制形式的RMS校准值
    rmsgn_decimal_1:
      name: "CH_11 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_2:
      name: "CH_12 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_3:
      name: "CH_13 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_4:
      name: "CH_14 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_5:
      name: "CH_15 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_6:
      name: "CH_16 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_1:
      name: "CH_11 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_2:
      name: "CH_12 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_3:
      name: "CH_13 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_4:
      name: "CH_14 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_5:
      name: "CH_15 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_6:
      name: "CH_16 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current



  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0910
    
    # BL0910支持10个通道的电流增益校准
    chgn_decimal_1:
      name: "CH_1 Current Gain"
      id: chgn_1_adjust
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_2:
      name: "CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_3:
      name: "CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_4:
      name: "CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_5:
      name: "CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_6:
      name: "CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    # BL0910扩展通道7-10的电流增益
    chgn_decimal_7:
      name: "CH_7 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_8:
      name: "CH_8 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_9:
      name: "CH_9 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_10:
      name: "CH_10 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    # 电压增益校准
    chgn_v_decimal:
      name: "Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: power

    # 电流偏移校准 - 10个通道
    chos_decimal_1:
      name: "CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_2:
      name: "CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_3:
      name: "CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_4:
      name: "CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_5:
      name: "CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_6:
      name: "CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    # BL0910扩展通道7-10的电流偏移
    chos_decimal_7:
      name: "CH_7 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_8:
      name: "CH_8 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_9:
      name: "CH_9 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_10:
      name: "CH_10 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    # 电压偏移校准
    chos_v_decimal:
      name: "Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: power
    
    # RMS增益校准 - 10个通道
    rmsgn_decimal_1:
      name: "CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_2:
      name: "CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_3:
      name: "CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_4:
      name: "CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_5:
      name: "CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_6:
      name: "CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    # BL0910扩展通道7-10的RMS增益
    rmsgn_decimal_7:
      name: "CH_7 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_8:
      name: "CH_8 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_9:
      name: "CH_9 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_10:
      name: "CH_10 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    # RMS偏移校准 - 10个通道
    rmsos_decimal_1:
      name: "CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_2:
      name: "CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_3:
      name: "CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_4:
      name: "CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_5:
      name: "CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_6:
      name: "CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    # BL0910扩展通道7-10的RMS偏移
    rmsos_decimal_7:
      name: "CH_7 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_8:
      name: "CH_8 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_9:
      name: "CH_9 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_10:
      name: "CH_10 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current
switch:
  # 电量持久化开关
  - platform: template
    name: "Energy Persistence 6ch"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "电量持久化存储已禁用");
  - platform: template
    name: "Energy Persistence 10ch"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
          bl0910->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0910 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0910));
          bl0910->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "电量持久化存储已禁用");
# 状态显示
text_sensor:
  - platform: template
    name: "System Info"
    icon: "mdi:chip"
    lambda: |-
      return {"Uptime: " + to_string(millis() / 1000) + "s, Free Heap: " + to_string(esp_get_free_heap_size()) + " bytes"};
    update_interval: 60s

