# BL0906 Factory - 批量修改和清零校准寄存器功能使用指南

## 功能概述

此功能提供了三种批量操作校准寄存器的方法：
1. **批量修改CHGN值**：通过传感器值批量设置所有CHGN（电流增益调整）寄存器
2. **批量清零CHGN值**：将所有CHGN寄存器重置为0
3. **批量清零RMSOS值**：将所有RMSOS（有效值偏置调整）寄存器重置为0

## 支持的寄存器

### CHGN寄存器（电流增益调整）
- **CHGN_1** (0xA1) - 电流通道1增益调整寄存器
- **CHGN_2** (0xA2) - 电流通道2增益调整寄存器
- **CHGN_3** (0xA3) - 电流通道3增益调整寄存器
- **CHGN_4** (0xA4) - 电流通道4增益调整寄存器
- **CHGN_5** (0xA7) - 电流通道5增益调整寄存器
- **CHGN_6** (0xA8) - 电流通道6增益调整寄存器
- **CHGN_V** (0xAA) - 电压通道增益调整寄存器

### RMSOS寄存器（有效值偏置调整）
- **RMSOS_1** (0x78) - 电流通道1有效值偏置调整寄存器
- **RMSOS_2** (0x79) - 电流通道2有效值偏置调整寄存器
- **RMSOS_3** (0x7A) - 电流通道3有效值偏置调整寄存器
- **RMSOS_4** (0x7B) - 电流通道4有效值偏置调整寄存器
- **RMSOS_5** (0x7E) - 电流通道5有效值偏置调整寄存器
- **RMSOS_6** (0x7F) - 电流通道6有效值偏置调整寄存器

## 配置步骤

### 1. 配置参考传感器

首先需要创建一个传感器作为CHGN值的来源：

```yaml
sensor:
  - platform: template
    name: "CHGN参考值"
    id: chgn_reference_value
    accuracy_decimals: 0
    lambda: |-
      // 返回你想要设置的CHGN值
      return 100;  // 示例值
    update_interval: 60s
```

### 2. 配置BL0906 Factory组件

在BL0906 Factory组件配置中添加参考传感器：

```yaml
bl0906_factory:
  id: bl0906_factory_1
  communication: uart  # 或 spi
  # 其他必要配置...
  
  # 设置CHGN参考传感器
  chgn_reference_sensor: chgn_reference_value
```

### 3. 配置触发按钮

创建模板按钮来触发批量操作：

```yaml
button:
  # 批量修改CHGN值
  - platform: template
    name: "批量更新所有CHGN值"
    id: update_all_chgn_button
    on_press:
      - lambda: |-
          id(bl0906_factory_1)->update_all_chgn_values_from_sensor();

  # 批量清零CHGN值
  - platform: template
    name: "批量清零所有CHGN值"
    id: reset_all_chgn_button
    on_press:
      - lambda: |-
          id(bl0906_factory_1)->reset_all_chgn_values_to_zero();

  # 批量清零RMSOS值
  - platform: template
    name: "批量清零所有RMSOS值"
    id: reset_all_rmsos_button
    on_press:
      - lambda: |-
          id(bl0906_factory_1)->reset_all_rmsos_values_to_zero();
```

## 使用方法

### 批量修改CHGN值
1. **设置参考值**：修改参考传感器的值（可以通过lambda函数、其他传感器的值等）
2. **触发更新**：点击"批量更新所有CHGN值"按钮
3. **查看结果**：查看日志输出，确认所有CHGN寄存器已更新

### 批量清零CHGN值
1. **触发清零**：点击"批量清零所有CHGN值"按钮
2. **查看结果**：查看日志输出，确认所有CHGN寄存器已清零

### 批量清零RMSOS值
1. **触发清零**：点击"批量清零所有RMSOS值"按钮
2. **查看结果**：查看日志输出，确认所有RMSOS寄存器已清零

## 日志输出示例

### 批量修改CHGN值
```
[I][bl0906_factory:1234] 开始批量修改所有CHGN值，参考值: 100
[I][bl0906_factory:1234] 通道1 CHGN(0xA1) 修改成功: 100
[I][bl0906_factory:1234] 通道2 CHGN(0xA2) 修改成功: 100
[I][bl0906_factory:1234] 通道3 CHGN(0xA3) 修改成功: 100
[I][bl0906_factory:1234] 通道4 CHGN(0xA4) 修改成功: 100
[I][bl0906_factory:1234] 通道5 CHGN(0xA7) 修改成功: 100
[I][bl0906_factory:1234] 通道6 CHGN(0xA8) 修改成功: 100
[I][bl0906_factory:1234] 电压通道 CHGN_V(0xAA) 修改成功: 100
[I][bl0906_factory:1234] 批量修改CHGN值完成: 7 成功，0 失败 (总计7)
[I][bl0906_factory:1234] ✅ 所有CHGN值批量修改成功
```

### 批量清零CHGN值
```
[I][bl0906_factory:1234] 开始批量清零所有CHGN值
[I][bl0906_factory:1234] 通道1 CHGN(0xA1) 清零成功
[I][bl0906_factory:1234] 通道2 CHGN(0xA2) 清零成功
[I][bl0906_factory:1234] 通道3 CHGN(0xA3) 清零成功
[I][bl0906_factory:1234] 通道4 CHGN(0xA4) 清零成功
[I][bl0906_factory:1234] 通道5 CHGN(0xA7) 清零成功
[I][bl0906_factory:1234] 通道6 CHGN(0xA8) 清零成功
[I][bl0906_factory:1234] 电压通道 CHGN_V(0xAA) 清零成功
[I][bl0906_factory:1234] 批量清零CHGN值完成: 7 成功，0 失败 (总计7)
[I][bl0906_factory:1234] ✅ 所有CHGN值批量清零成功
```

### 批量清零RMSOS值
```
[I][bl0906_factory:1234] 开始批量清零所有RMSOS值
[I][bl0906_factory:1234] 通道1 RMSOS(0x78) 清零成功
[I][bl0906_factory:1234] 通道2 RMSOS(0x79) 清零成功
[I][bl0906_factory:1234] 通道3 RMSOS(0x7A) 清零成功
[I][bl0906_factory:1234] 通道4 RMSOS(0x7B) 清零成功
[I][bl0906_factory:1234] 通道5 RMSOS(0x7E) 清零成功
[I][bl0906_factory:1234] 通道6 RMSOS(0x7F) 清零成功
[I][bl0906_factory:1234] 批量清零RMSOS值完成: 6 成功，0 失败 (总计6)
[I][bl0906_factory:1234] ✅ 所有RMSOS值批量清零成功
```

## 注意事项

### 通用注意事项
1. **数据类型**：CHGN和RMSOS寄存器都为16位补码，有效范围为-32768到32767
2. **写保护**：所有函数都会自动处理写保护的开启和关闭
3. **延时保护**：函数内部包含适当的延时以防止写入过快和看门狗复位
4. **自动刷新**：操作完成后会自动刷新所有校准数字组件的显示

### 批量修改CHGN值特定注意事项
1. **参考传感器**：确保在bl0906_factory配置中正确设置了`chgn_reference_sensor`
2. **传感器值检查**：如果参考传感器未设置或状态无效，操作将被取消
3. **值转换**：参考传感器的值会被转换为int16_t类型

### 批量清零功能特定注意事项
1. **CHGN清零**：会清零所有7个CHGN寄存器（6个电流通道 + 1个电压通道）
2. **RMSOS清零**：会清零所有6个RMSOS寄存器（仅电流通道，无电压通道）
3. **不可逆操作**：清零操作不可逆，执行前请确认

## 高级用法

### 动态参考值

可以使用其他传感器的值作为参考：

```yaml
sensor:
  - platform: template
    name: "动态CHGN值"
    id: dynamic_chgn_value
    lambda: |-
      // 基于其他传感器的值计算CHGN
      float base_current = id(some_current_sensor).state;
      if (base_current > 0) {
        return (int)(base_current * 10);  // 示例计算
      }
      return 0;
```

### 条件更新

可以在lambda中添加条件判断：

```yaml
button:
  - platform: template
    name: "条件更新CHGN"
    on_press:
      - lambda: |-
          if (id(chgn_reference_value).state > 0) {
            id(bl0906_factory_1)->update_all_chgn_values_from_sensor();
          } else {
            ESP_LOGW("main", "CHGN参考值无效，跳过更新");
          }
```

## 故障排除

1. **参考传感器未设置**：确保在bl0906_factory配置中正确设置了`chgn_reference_sensor`
2. **传感器值无效**：检查参考传感器是否正常工作并提供有效值
3. **写入失败**：检查通信连接和BL0906硬件状态
4. **部分寄存器更新失败**：可能是硬件问题或通信不稳定，请检查日志具体错误

## 完整示例

参考 `test_chgn_batch_update.yaml` 文件查看完整的配置示例。 