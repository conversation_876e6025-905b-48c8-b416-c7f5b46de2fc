#include "energy_statistics_manager.h"
#include "bl0906_factory.h"
#include "bl0906_calibration.h"
#include "esphome/core/log.h"
#include "esphome/components/time/real_time_clock.h"
#include "esphome/core/hal.h"
#include "esphome/core/application.h"

// 添加使用命名空间，以便可以访问常量
using namespace esphome::bl0906_factory;

// 避免命名空间冲突
namespace esphome_time = esphome::time;

namespace esphome {
namespace bl0906_factory {

static const char *const ENERGY_STATS_TAG = "energy_stats";

EnergyStatisticsManager::EnergyStatisticsManager(BL0906Factory* parent) : PollingComponent(60000), parent_(parent) {
  // 初始化原子变量
  for (int i = 0; i < ARRAY_SIZE; i++) {
    current_persistent_cf_count_[i].store(0);
  }
  initialized_.store(false);
  last_check_timestamp_.store(0);
}

void EnergyStatisticsManager::setup() {
  ESP_LOGI(ENERGY_STATS_TAG, "初始化电量统计管理器...");
  
  // 喂狗，防止看门狗超时
  App.feed_wdt();
  
  // 加载统计数据
  load_statistics_data_safe();
  
  // 喂狗，防止看门狗超时
  App.feed_wdt();
  
  // 检查快照是否为0，如果为0则立即生成快照
  bool need_create_snapshot = false;
  {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    // 检查各通道的快照是否都为0
    for (int channel = 0; channel < 7; channel++) {  // 7个元素：6个通道 + 1个总和
      for (int period = 0; period < 5; period++) {   // 5个周期
        if (unified_statistics_.period_persistent_cf_count[channel][period] == 0) {
          need_create_snapshot = true;
          ESP_LOGI(ENERGY_STATS_TAG, "检测到通道%d周期%d的快照为0，需要生成快照", 
                   channel < 6 ? channel + 1 : 7, period);
          break;
        }
      }
      if (need_create_snapshot) {
        break;
      }
      
      // 每处理几个通道就喂一次狗
      if (channel % 2 == 0) {
        App.feed_wdt();
      }
    }
    
    // 检查时间快照是否为0
    if (!need_create_snapshot) {
      for (int i = 0; i < 5; i++) {
        if (unified_statistics_.period_times[i].timestamp == 0) {
          need_create_snapshot = true;
          ESP_LOGI(ENERGY_STATS_TAG, "检测到周期%d的时间快照为0，需要生成快照", i);
          break;
        }
      }
    }
  }
  
  // 喂狗，防止看门狗超时
  App.feed_wdt();
  
  // 如果需要生成快照且有时间组件，创建初始快照
  if (need_create_snapshot && time_component_ && is_time_valid()) {
    ESP_LOGI(ENERGY_STATS_TAG, "立即触发生成初始快照");
    create_current_snapshot_safe();
  } else if (time_component_ && is_time_valid() && !need_create_snapshot) {
    ESP_LOGI(ENERGY_STATS_TAG, "快照数据完整，跳过快照生成");
  } else if (!time_component_ || !is_time_valid()) {
    ESP_LOGW(ENERGY_STATS_TAG, "时间组件无效，无法生成快照");
  }
  
  // 喂狗，防止看门狗超时
  App.feed_wdt();
  
  initialized_.store(true);
  ESP_LOGI(ENERGY_STATS_TAG, "电量统计管理器初始化完成");
}

void EnergyStatisticsManager::set_time_component(esphome_time::RealTimeClock *time_comp) {
  time_component_ = time_comp;
  ESP_LOGI(ENERGY_STATS_TAG, "设置时间组件: %s", time_comp ? "成功" : "无效");
}

void EnergyStatisticsManager::update_persistent_cf_count(int channel, uint32_t cf_count_increment) {
  if (channel < 0 || channel >= CHANNEL_COUNT) {
    ESP_LOGW(ENERGY_STATS_TAG, "无效通道: %d", channel);
    return;
  }
  
  // 使用原子操作更新持久化CF_count
  current_persistent_cf_count_[channel].fetch_add(cf_count_increment);
  
  ESP_LOGD(ENERGY_STATS_TAG, "通道%d持久化CF_count增加: %u", channel + 1, cf_count_increment);
}

void EnergyStatisticsManager::update_persistent_cf_count_sum(uint32_t cf_count_increment) {
  // 总和存储在数组的第7个元素（索引6）
  current_persistent_cf_count_[6].fetch_add(cf_count_increment);
  
  ESP_LOGD(ENERGY_STATS_TAG, "总持久化CF_count增加: %u", cf_count_increment);
}

void EnergyStatisticsManager::check_period_changes() {
  if (!initialized_.load() || !time_component_ || !is_time_valid()) {
    return;
  }
  
  ESPTime current_time = get_current_time();
  time_t current_timestamp = current_time.timestamp;
  time_t last_timestamp = last_check_timestamp_.load();
  
  // 避免频繁检查（至少间隔1分钟）
  if (current_timestamp - last_timestamp < 60) {
    return;
  }
  
  // 更新检查时间戳
  last_check_timestamp_.store(current_timestamp);
  
  // 如果是第一次检查，不执行周期变化处理
  if (last_timestamp == 0) {
    return;
  }
  
  ESPTime last_time;
  last_time.timestamp = last_timestamp;
  last_time.recalc_timestamp_utc();
  
  // 检查各种周期变化
  if (is_new_year(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新年: %d -> %d", last_time.year, current_time.year);
    handle_new_year();
  } else if (is_new_month(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新月: %d-%d -> %d-%d", 
             last_time.year, last_time.month, current_time.year, current_time.month);
    handle_new_month();
  } else if (is_new_week(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新周");
    handle_new_week();
  } else if (is_new_day(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新日: %d-%d-%d -> %d-%d-%d",
             last_time.year, last_time.month, last_time.day_of_month,
             current_time.year, current_time.month, current_time.day_of_month);
    handle_new_day();
  }
}

void EnergyStatisticsManager::update_statistics_on_save() {
  if (!initialized_.load()) {
    return;
  }
  
  // 线程安全地更新统计数据
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  // 防止并发更新
  if (unified_statistics_.updating.exchange(true)) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计数据正在更新中，跳过本次保存更新");
    return;
  }
  
  // 更新当前持久化CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }
  
  ESPTime current_time = get_current_time();
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  unified_statistics_.updating.store(false);
  
  ESP_LOGD(ENERGY_STATS_TAG, "统计数据在保存时已更新");
}

void EnergyStatisticsManager::update_statistics_on_date_change() {
  // 这个方法在日期变更时被调用，用于更新时间点快照
  // 具体实现在handle_new_day等方法中
  ESP_LOGD(ENERGY_STATS_TAG, "日期变更时更新统计数据");
}

void EnergyStatisticsManager::update() {
  if (!initialized_.load()) {
    return;
  }
  
  // 更新各通道的统计传感器
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    // 昨日电量
    if (yesterday_energy_sensors_[channel]) {
      float energy = (channel < CHANNEL_COUNT) ? 
        calculate_energy_for_period(channel, EnergyPeriod::YESTERDAY) :
        calculate_total_energy_for_period(EnergyPeriod::YESTERDAY);
      yesterday_energy_sensors_[channel]->publish_state(energy);
    }
    
    // 今日电量
    if (today_energy_sensors_[channel]) {
      float energy = (channel < CHANNEL_COUNT) ? 
        calculate_energy_for_period(channel, EnergyPeriod::TODAY) :
        calculate_total_energy_for_period(EnergyPeriod::TODAY);
      today_energy_sensors_[channel]->publish_state(energy);
    }
    
    // 本周电量
    if (week_energy_sensors_[channel]) {
      float energy = (channel < CHANNEL_COUNT) ? 
        calculate_energy_for_period(channel, EnergyPeriod::THIS_WEEK) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_WEEK);
      week_energy_sensors_[channel]->publish_state(energy);
    }
    
    // 本月电量
    if (month_energy_sensors_[channel]) {
      float energy = (channel < CHANNEL_COUNT) ? 
        calculate_energy_for_period(channel, EnergyPeriod::THIS_MONTH) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_MONTH);
      month_energy_sensors_[channel]->publish_state(energy);
    }
    
    // 本年电量
    if (year_energy_sensors_[channel]) {
      float energy = (channel < CHANNEL_COUNT) ? 
        calculate_energy_for_period(channel, EnergyPeriod::THIS_YEAR) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_YEAR);
      year_energy_sensors_[channel]->publish_state(energy);
    }
  }
  
  // 检查周期变化
  check_period_changes();
  
  ESP_LOGD(ENERGY_STATS_TAG, "统计传感器已更新");
}

void EnergyStatisticsManager::set_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel) {
  if (channel < 0 || channel >= ARRAY_SIZE) {
    ESP_LOGW(ENERGY_STATS_TAG, "无效通道索引: %d", channel);
    return;
  }
  
  switch (type) {
    case StatisticsSensorType::YESTERDAY_ENERGY:
    case StatisticsSensorType::YESTERDAY_TOTAL_ENERGY:
      yesterday_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::TODAY_ENERGY:
    case StatisticsSensorType::TODAY_TOTAL_ENERGY:
      today_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::WEEK_ENERGY:
    case StatisticsSensorType::WEEK_TOTAL_ENERGY:
      week_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::MONTH_ENERGY:
    case StatisticsSensorType::MONTH_TOTAL_ENERGY:
      month_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::YEAR_ENERGY:
    case StatisticsSensorType::YEAR_TOTAL_ENERGY:
      year_energy_sensors_[channel] = sensor;
      break;
  }
  
  ESP_LOGI(ENERGY_STATS_TAG, "设置统计传感器: 类型=%d, 通道=%d", static_cast<int>(type), channel);
}

// 线程安全的数据访问方法
void EnergyStatisticsManager::get_statistics_data_safe(OptimizedEnergyStatistics& out_data) const {
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  out_data = unified_statistics_;
}

void EnergyStatisticsManager::set_statistics_data_safe(const OptimizedEnergyStatistics& in_data) {
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  unified_statistics_ = in_data;
}

// 时间管理方法
ESPTime EnergyStatisticsManager::get_current_time() const {
  if (!time_component_) {
    return {};
  }
  return time_component_->now();
}

bool EnergyStatisticsManager::is_time_valid() const {
  if (!time_component_) {
    return false;
  }
  ESPTime now = time_component_->now();
  return now.is_valid() && now.year > 2020; // 基本有效性检查
}

CompactTimeSnapshot EnergyStatisticsManager::create_time_snapshot(const ESPTime &time) const {
  CompactTimeSnapshot snapshot;
  snapshot.timestamp = time.timestamp;
  snapshot.year = time.year;
  snapshot.month = time.month;
  snapshot.day_of_month = time.day_of_month;
  snapshot.day_of_week = time.day_of_week;
  return snapshot;
}

bool EnergyStatisticsManager::is_new_day(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.day_of_month != current_time.day_of_month) ||
         (last_time.month != current_time.month) ||
         (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_week(const ESPTime &last_time, const ESPTime &current_time) const {
  // 跨年处理
  if (last_time.year != current_time.year) {
    return true;
  }
  
  // 使用day_of_year和day_of_week精确计算周边界
  int last_week_start = last_time.day_of_year - last_time.day_of_week;
  int current_week_start = current_time.day_of_year - current_time.day_of_week;
  
  return last_week_start != current_week_start;
}

bool EnergyStatisticsManager::is_new_month(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.month != current_time.month) || (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_year(const ESPTime &last_time, const ESPTime &current_time) const {
  return last_time.year != current_time.year;
}

// 周期处理方法
void EnergyStatisticsManager::handle_new_day() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一天");
  
  // 线程安全地更新统计数据
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  // 防止并发更新
  if (unified_statistics_.updating.exchange(true)) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计数据正在更新中，跳过新日处理");
    return;
  }
  
  // 创建当前时刻的时间快照
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 更新当前持久化CF_count（避免调用update_statistics_on_save造成递归）
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }
  
  // 更新时间点快照（所有通道共享）
  // 昨日开始 = 之前的今日开始
  unified_statistics_.period_times[0] = unified_statistics_.period_times[1]; // 昨日开始
  // 今日开始 = 当前时刻
  unified_statistics_.period_times[1] = time_snapshot; // 今日开始
  
  // 更新各通道的持久化CF_count快照
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    // 昨日开始CF_count = 之前的今日开始CF_count
    unified_statistics_.period_persistent_cf_count[channel][0] = unified_statistics_.period_persistent_cf_count[channel][1];
    // 今日开始CF_count = 当前持久化CF_count
    unified_statistics_.period_persistent_cf_count[channel][1] = current_persistent_cf_count_[channel].load();
  }
  
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  
  // 更新完成标志
  unified_statistics_.updating.store(false);
  
  // 注意：这里不调用save_statistics_data_safe()，避免递归调用
  // 数据会在下次正常的save_energy_data()调用时被保存
  ESP_LOGI(ENERGY_STATS_TAG, "新日统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_week() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一周");
  
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  if (unified_statistics_.updating.exchange(true)) {
    return;
  }
  
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 更新当前持久化CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }
  
  // 更新本周开始时间
  unified_statistics_.period_times[2] = time_snapshot;
  
  // 更新各通道的本周开始CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.period_persistent_cf_count[channel][2] = current_persistent_cf_count_[channel].load();
  }
  
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  unified_statistics_.updating.store(false);
  
  ESP_LOGI(ENERGY_STATS_TAG, "新周统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_month() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一月");
  
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  if (unified_statistics_.updating.exchange(true)) {
    return;
  }
  
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 更新当前持久化CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }
  
  // 更新本月开始时间
  unified_statistics_.period_times[3] = time_snapshot;
  
  // 更新各通道的本月开始CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.period_persistent_cf_count[channel][3] = current_persistent_cf_count_[channel].load();
  }
  
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  unified_statistics_.updating.store(false);
  
  ESP_LOGI(ENERGY_STATS_TAG, "新月统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_year() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一年");
  
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  if (unified_statistics_.updating.exchange(true)) {
    return;
  }
  
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 更新当前持久化CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }
  
  // 更新本年开始时间
  unified_statistics_.period_times[4] = time_snapshot;
  
  // 更新各通道的本年开始CF_count
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    unified_statistics_.period_persistent_cf_count[channel][4] = current_persistent_cf_count_[channel].load();
  }
  
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  unified_statistics_.updating.store(false);
  
  ESP_LOGI(ENERGY_STATS_TAG, "新年统计数据更新完成，等待下次保存周期");
}

// 快照管理
void EnergyStatisticsManager::create_current_snapshot_safe() {
  if (!is_time_valid()) {
    return;
  }
  
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 初始化所有周期的时间快照为当前时间
  for (int i = 0; i < 5; i++) {
    unified_statistics_.period_times[i] = time_snapshot;
  }
  
  // 初始化所有通道的CF_count快照为当前值
  for (int channel = 0; channel < ARRAY_SIZE; channel++) {
    uint32_t current_cf = current_persistent_cf_count_[channel].load();
    for (int period = 0; period < 5; period++) {
      unified_statistics_.period_persistent_cf_count[channel][period] = current_cf;
    }
    unified_statistics_.current_persistent_cf_count[channel] = current_cf;
  }
  
  unified_statistics_.last_update_timestamp = current_time.timestamp;
  
  ESP_LOGI(ENERGY_STATS_TAG, "创建初始时间快照完成");
}

// 电量计算方法
float EnergyStatisticsManager::calculate_energy_for_period(int channel, EnergyPeriod period) const {
  if (channel < 0 || channel >= ARRAY_SIZE) {
    return 0.0f;
  }
  
  // 线程安全地读取统计数据
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  uint32_t start_cf_count = 0;
  uint32_t current_cf_count = unified_statistics_.current_persistent_cf_count[channel];
  
  // 获取对应周期的起始持久化CF_count
  switch (period) {
    case EnergyPeriod::YESTERDAY:
      // 昨日电量 = (今日开始CF_count - 昨日开始CF_count) / Ke
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][0];  // 昨日开始
      current_cf_count = unified_statistics_.period_persistent_cf_count[channel][1]; // 今日开始
      break;
    case EnergyPeriod::TODAY:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][1];   // 今日开始
      break;
    case EnergyPeriod::THIS_WEEK:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][2];   // 本周开始
      break;
    case EnergyPeriod::THIS_MONTH:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][3];   // 本月开始
      break;
    case EnergyPeriod::THIS_YEAR:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][4];   // 本年开始
      break;
  }
  
  // 计算CF_count差值
  uint32_t count_diff;
  if (current_cf_count >= start_cf_count) {
    count_diff = current_cf_count - start_cf_count;
  } else {
    // 处理溢出情况
    count_diff = (0xFFFFFFFF - start_cf_count) + current_cf_count + 1;
    ESP_LOGD(ENERGY_STATS_TAG, "通道%d检测到CF_count溢出: %u -> %u", 
             channel + 1, start_cf_count, current_cf_count);
  }
  
  // 转换为电量（kWh）
  float period_energy = count_diff / esphome::bl0906_factory::Ke;
  
  // 确保电量值不为负
  if (period_energy < 0.0f) {
    ESP_LOGW(ENERGY_STATS_TAG, "通道%d的%d周期电量为负值: %.6f，重置为0", 
             channel + 1, static_cast<int>(period), period_energy);
    period_energy = 0.0f;
  }
  
  return period_energy;
}

float EnergyStatisticsManager::calculate_total_energy_for_period(EnergyPeriod period) const {
  // 总电量使用数组的第7个元素（索引6）
  return calculate_energy_for_period(6, period);
}

// 数据持久化方法
void EnergyStatisticsManager::save_statistics_data_safe() {
  // 通过parent_调用BL0906Factory的保存方法
  if (parent_) {
    parent_->save_energy_data();
  }
}

void EnergyStatisticsManager::load_statistics_data_safe() {
  // 通过parent_调用BL0906Factory的加载方法
  if (parent_) {
    parent_->load_energy_data();
  }
}

}  // namespace bl0906_factory
}  // namespace esphome 