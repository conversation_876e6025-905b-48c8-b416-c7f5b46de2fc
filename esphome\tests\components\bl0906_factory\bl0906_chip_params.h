#pragma once

// BL0906/BL0910芯片参数条件编译配置
// 根据编译时宏定义选择对应的芯片参数

// 根据芯片型号条件编译
#ifdef BL0906_FACTORY_CHIP_MODEL_BL0910
  // BL0910 参数
  #define CHIP_NAME "BL0910"
  #define CHANNEL_COUNT 10
  #define I_RMS_ADDRESSES {0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15}
  #define WATT_ADDRESSES  {0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B}
  #define CF_CNT_ADDRESSES {0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38}
  #define RMSGN_ADDRESSES {0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76}
  #define RMSOS_ADDRESSES {0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F, 0x80, 0x81}  // BL0910 RMSOS地址
  #define CHGN_ADDRESSES  {0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74}
  #define CHOS_ADDRESSES  {0xAC, 0xAD, 0xAE, 0xAF, 0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5}  // BL0910 CHOS地址
  #define WATTGN_ADDRESSES {0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF, 0xC0} // BL0910 WATTGN地址
  #define WATTOS_ADDRESSES {0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA} // BL0910 WATTOS地址
  
  // BL0910 特有寄存器地址
  #define V_RMS_ADDR      0x16
  #define FREQUENCY_ADDR  0x4E
  #define TEMPERATURE_ADDR 0x5E
  
  // BL0910 公共寄存器
  #define WATT_SUM_ADDR   0x2C
  #define CF_SUM_ADDR     0x39
  
#else
  // BL0906 参数（默认）- 与bl0906_registers.h中的寄存器定义保持一致
  #define CHIP_NAME "BL0906"
  #define CHANNEL_COUNT 6
  #define I_RMS_ADDRESSES {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14}
  #define WATT_ADDRESSES  {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A}
  #define CF_CNT_ADDRESSES {0x30, 0x31, 0x32, 0x33, 0x36, 0x37}
  #define RMSGN_ADDRESSES {0x6D, 0x6E, 0x6F, 0x70, 0x73, 0x74}  // 对应BL0906_RMSGN_1-6
  #define RMSOS_ADDRESSES {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F}  // 对应BL0906_RMSOS_1-6
  #define CHGN_ADDRESSES  {0xA1, 0xA2, 0xA3, 0xA4, 0xA7, 0xA8}  // 对应BL0906_CHGN_1-6
  #define CHOS_ADDRESSES  {0xAC, 0xAD, 0xAE, 0xAF, 0xB2, 0xB3}  // 对应BL0906_CHOS_1-6
  #define WATTGN_ADDRESSES {0xB7, 0xB8, 0xB9, 0xBA, 0xBD, 0xBE} // 对应BL0906_WATTGN_1-6
  #define WATTOS_ADDRESSES {0xC1, 0xC2, 0xC3, 0xC4, 0xC7, 0xC8} // 对应BL0906_WATTOS_1-6
  
  // BL0906 寄存器地址 - 修正为与bl0906_registers.h一致的地址
  #define V_RMS_ADDR      0x16
  #define FREQUENCY_ADDR  0x4E
  #define TEMPERATURE_ADDR 0x5E
  
  // BL0906 公共寄存器
  #define WATT_SUM_ADDR   0x2C
  #define CF_SUM_ADDR     0x39
#endif

// 编译时常量数组和命名空间定义
namespace esphome {
namespace bl0906_factory {

// ========== 操作命令定义 ==========
// UART命令
static constexpr uint8_t BL0906_UART_READ_COMMAND = 0x35;   // UART读操作命令
static constexpr uint8_t BL0906_UART_WRITE_COMMAND = 0xCA;  // UART写操作命令

// SPI命令
static constexpr uint8_t BL0906_SPI_READ_COMMAND = 0x82;    // SPI读操作命令
static constexpr uint8_t BL0906_SPI_WRITE_COMMAND = 0x81;   // SPI写操作命令

// 为了兼容性，保留旧的常量名（但现在通信适配器应该使用具体的常量）
#ifdef USE_BL0906_FACTORY_SPI
static constexpr uint8_t BL0906_READ_COMMAND = BL0906_SPI_READ_COMMAND;
static constexpr uint8_t BL0906_WRITE_COMMAND = BL0906_SPI_WRITE_COMMAND;
#else
static constexpr uint8_t BL0906_READ_COMMAND = BL0906_UART_READ_COMMAND;
static constexpr uint8_t BL0906_WRITE_COMMAND = BL0906_UART_WRITE_COMMAND;
#endif

// ========== 编译时常量数组 ==========
static constexpr uint8_t I_RMS_ADDR[CHANNEL_COUNT] = I_RMS_ADDRESSES;
static constexpr uint8_t WATT_ADDR[CHANNEL_COUNT] = WATT_ADDRESSES;
static constexpr uint8_t CF_CNT_ADDR[CHANNEL_COUNT] = CF_CNT_ADDRESSES;
static constexpr uint8_t RMSGN_ADDR[CHANNEL_COUNT] = RMSGN_ADDRESSES;
static constexpr uint8_t RMSOS_ADDR[CHANNEL_COUNT] = RMSOS_ADDRESSES;
static constexpr uint8_t CHGN_ADDR[CHANNEL_COUNT] = CHGN_ADDRESSES;
static constexpr uint8_t CHOS_ADDR[CHANNEL_COUNT] = CHOS_ADDRESSES;
static constexpr uint8_t WATTGN_ADDR[CHANNEL_COUNT] = WATTGN_ADDRESSES;
static constexpr uint8_t WATTOS_ADDR[CHANNEL_COUNT] = WATTOS_ADDRESSES;

// ========== 公共寄存器地址 ==========
static constexpr uint8_t BL0906_V_RMS = V_RMS_ADDR;           // 电压通道有效值寄存器
static constexpr uint8_t BL0906_FREQUENCY = FREQUENCY_ADDR;   // 线电压频率周期，无符号
static constexpr uint8_t BL0906_TEMPERATURE = TEMPERATURE_ADDR; // 内部温度值，无符号
static constexpr uint8_t BL0906_WATT_SUM = WATT_SUM_ADDR;     // 总有功功率寄存器
static constexpr uint8_t BL0906_CF_SUM_CNT = CF_SUM_ADDR;     // 总有功脉冲计数，无符号

// ========== 工作模式寄存器 ==========
static constexpr uint8_t BL0906_MODE2 = 0x97;  // 工作模式寄存器2

// MODE2寄存器位掩码定义
static constexpr uint32_t MODE2_AC_FREQ_SEL_MASK = 0x800000;  // 第23位
static constexpr uint32_t MODE2_AC_FREQ_50HZ = 0x000000;     // 50Hz
static constexpr uint32_t MODE2_AC_FREQ_60HZ = 0x800000;     // 60Hz

// ========== 写保护控制指令 ==========
static constexpr uint8_t USR_WRPROT_ENABLE = 0x55;  // 用户寄存器可操作指令的一部分
static constexpr uint8_t USR_WRPROT_DISABLE = 0x00; // 用户寄存器只读指令的一部分

// ========== 编译时常量 ==========
static constexpr int MAX_CHANNELS = CHANNEL_COUNT;
static constexpr const char* CHIP_MODEL_NAME = CHIP_NAME;

// 数组大小常量（需要包含总和通道）
static constexpr int ARRAY_SIZE = CHANNEL_COUNT + 1;  // 通道数 + 1个总和

// ========== 寄存器类型判断函数 ==========

/**
 * 判断寄存器是否为16位寄存器
 * @param address 寄存器地址
 * @return true 如果是16位寄存器，false 否则
 */
inline bool is_16bit_register(uint8_t address) {
  // 检查是否在CHGN地址数组中
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == CHGN_ADDR[i] || address == CHOS_ADDR[i] || 
        address == RMSGN_ADDR[i] || address == WATTGN_ADDR[i] || 
        address == WATTOS_ADDR[i]) {
      return true;
    }
  }
  // 检查电压校准寄存器（通过偏移量计算）
  return (address == CHGN_ADDR[0] + (CHANNEL_COUNT + 1)) ||  // CHGN_V估算位置
         (address == CHOS_ADDR[0] + (CHANNEL_COUNT + 3));    // CHOS_V估算位置
}

/**
 * 判断寄存器是否为无符号类型
 * @param address 寄存器地址
 * @return true 如果是无符号寄存器，false 否则
 */
inline bool is_unsigned_register(uint8_t address) {
  // 检查公共寄存器
  if (address == BL0906_FREQUENCY || address == BL0906_TEMPERATURE || 
      address == BL0906_V_RMS || address == BL0906_CF_SUM_CNT || 
      address == BL0906_MODE2) {
    return true;
  }
  
  // 检查通道寄存器
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == I_RMS_ADDR[i] || address == CF_CNT_ADDR[i]) {
      return true;
    }
  }
  
  return false;  // 其他寄存器默认为有符号
}

/**
 * 判断寄存器是否为24位类型
 * @param address 寄存器地址
 * @return true 如果是24位寄存器，false 否则
 */
inline bool is_24bit_register(uint8_t address) {
  // MODE2寄存器是24位
  if (address == BL0906_MODE2) {
    return true;
  }
  
  // 检查RMSOS寄存器数组
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == RMSOS_ADDR[i]) {
      return true;
    }
  }
  
  return false;
}

// ========== 编译时验证 ==========
static_assert(CHANNEL_COUNT == 6 || CHANNEL_COUNT == 10, 
              "Invalid channel count - must be 6 (BL0906) or 10 (BL0910)");

static_assert(sizeof(I_RMS_ADDR) / sizeof(I_RMS_ADDR[0]) == CHANNEL_COUNT,
              "I_RMS address array size mismatch");

static_assert(sizeof(WATT_ADDR) / sizeof(WATT_ADDR[0]) == CHANNEL_COUNT,
              "WATT address array size mismatch");

static_assert(sizeof(CF_CNT_ADDR) / sizeof(CF_CNT_ADDR[0]) == CHANNEL_COUNT,
              "CF_CNT address array size mismatch");

// ========== 具体寄存器常量定义（基于数组动态生成） ==========
// 电流有效值寄存器
static constexpr uint8_t BL0906_I_1_RMS = I_RMS_ADDR[0];
static constexpr uint8_t BL0906_I_2_RMS = I_RMS_ADDR[1];
static constexpr uint8_t BL0906_I_3_RMS = I_RMS_ADDR[2];
static constexpr uint8_t BL0906_I_4_RMS = I_RMS_ADDR[3];
static constexpr uint8_t BL0906_I_5_RMS = I_RMS_ADDR[4];
static constexpr uint8_t BL0906_I_6_RMS = I_RMS_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_I_7_RMS = I_RMS_ADDR[6];
static constexpr uint8_t BL0906_I_8_RMS = I_RMS_ADDR[7];
static constexpr uint8_t BL0906_I_9_RMS = I_RMS_ADDR[8];
static constexpr uint8_t BL0906_I_10_RMS = I_RMS_ADDR[9];
#endif

// 功率寄存器
static constexpr uint8_t BL0906_WATT_1 = WATT_ADDR[0];
static constexpr uint8_t BL0906_WATT_2 = WATT_ADDR[1];
static constexpr uint8_t BL0906_WATT_3 = WATT_ADDR[2];
static constexpr uint8_t BL0906_WATT_4 = WATT_ADDR[3];
static constexpr uint8_t BL0906_WATT_5 = WATT_ADDR[4];
static constexpr uint8_t BL0906_WATT_6 = WATT_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_WATT_7 = WATT_ADDR[6];
static constexpr uint8_t BL0906_WATT_8 = WATT_ADDR[7];
static constexpr uint8_t BL0906_WATT_9 = WATT_ADDR[8];
static constexpr uint8_t BL0906_WATT_10 = WATT_ADDR[9];
#endif

// 能量脉冲计数寄存器
static constexpr uint8_t BL0906_CF_1_CNT = CF_CNT_ADDR[0];
static constexpr uint8_t BL0906_CF_2_CNT = CF_CNT_ADDR[1];
static constexpr uint8_t BL0906_CF_3_CNT = CF_CNT_ADDR[2];
static constexpr uint8_t BL0906_CF_4_CNT = CF_CNT_ADDR[3];
static constexpr uint8_t BL0906_CF_5_CNT = CF_CNT_ADDR[4];
static constexpr uint8_t BL0906_CF_6_CNT = CF_CNT_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_CF_7_CNT = CF_CNT_ADDR[6];
static constexpr uint8_t BL0906_CF_8_CNT = CF_CNT_ADDR[7];
static constexpr uint8_t BL0906_CF_9_CNT = CF_CNT_ADDR[8];
static constexpr uint8_t BL0906_CF_10_CNT = CF_CNT_ADDR[9];
#endif

// 有效值增益校准寄存器
static constexpr uint8_t BL0906_RMSGN_1 = RMSGN_ADDR[0];
static constexpr uint8_t BL0906_RMSGN_2 = RMSGN_ADDR[1];
static constexpr uint8_t BL0906_RMSGN_3 = RMSGN_ADDR[2];
static constexpr uint8_t BL0906_RMSGN_4 = RMSGN_ADDR[3];
static constexpr uint8_t BL0906_RMSGN_5 = RMSGN_ADDR[4];
static constexpr uint8_t BL0906_RMSGN_6 = RMSGN_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_RMSGN_7 = RMSGN_ADDR[6];
static constexpr uint8_t BL0906_RMSGN_8 = RMSGN_ADDR[7];
static constexpr uint8_t BL0906_RMSGN_9 = RMSGN_ADDR[8];
static constexpr uint8_t BL0906_RMSGN_10 = RMSGN_ADDR[9];
#endif
static constexpr uint8_t BL0906_RMSGN_V = 0x76; // 电压通道有效值增益调整寄存器

// 有效值偏置校准寄存器
static constexpr uint8_t BL0906_RMSOS_1 = RMSOS_ADDR[0];
static constexpr uint8_t BL0906_RMSOS_2 = RMSOS_ADDR[1];
static constexpr uint8_t BL0906_RMSOS_3 = RMSOS_ADDR[2];
static constexpr uint8_t BL0906_RMSOS_4 = RMSOS_ADDR[3];
static constexpr uint8_t BL0906_RMSOS_5 = RMSOS_ADDR[4];
static constexpr uint8_t BL0906_RMSOS_6 = RMSOS_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_RMSOS_7 = RMSOS_ADDR[6];
static constexpr uint8_t BL0906_RMSOS_8 = RMSOS_ADDR[7];
static constexpr uint8_t BL0906_RMSOS_9 = RMSOS_ADDR[8];
static constexpr uint8_t BL0906_RMSOS_10 = RMSOS_ADDR[9];
#endif
static constexpr uint8_t BL0906_RMSOS_V = 0x81; // 电压通道有效值偏置校正寄存器

// 电流通道增益校准寄存器
static constexpr uint8_t BL0906_CHGN_1 = CHGN_ADDR[0];
static constexpr uint8_t BL0906_CHGN_2 = CHGN_ADDR[1];
static constexpr uint8_t BL0906_CHGN_3 = CHGN_ADDR[2];
static constexpr uint8_t BL0906_CHGN_4 = CHGN_ADDR[3];
static constexpr uint8_t BL0906_CHGN_5 = CHGN_ADDR[4];
static constexpr uint8_t BL0906_CHGN_6 = CHGN_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_CHGN_7 = CHGN_ADDR[6];
static constexpr uint8_t BL0906_CHGN_8 = CHGN_ADDR[7];
static constexpr uint8_t BL0906_CHGN_9 = CHGN_ADDR[8];
static constexpr uint8_t BL0906_CHGN_10 = CHGN_ADDR[9];
#endif
static constexpr uint8_t BL0906_CHGN_V = 0xAA; // 电压通道增益调整寄存器

// 电流通道偏置校准寄存器
static constexpr uint8_t BL0906_CHOS_1 = CHOS_ADDR[0];
static constexpr uint8_t BL0906_CHOS_2 = CHOS_ADDR[1];
static constexpr uint8_t BL0906_CHOS_3 = CHOS_ADDR[2];
static constexpr uint8_t BL0906_CHOS_4 = CHOS_ADDR[3];
static constexpr uint8_t BL0906_CHOS_5 = CHOS_ADDR[4];
static constexpr uint8_t BL0906_CHOS_6 = CHOS_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_CHOS_7 = CHOS_ADDR[6];
static constexpr uint8_t BL0906_CHOS_8 = CHOS_ADDR[7];
static constexpr uint8_t BL0906_CHOS_9 = CHOS_ADDR[8];
static constexpr uint8_t BL0906_CHOS_10 = CHOS_ADDR[9];
#endif
static constexpr uint8_t BL0906_CHOS_V = 0xB5; // 电压通道偏置调整寄存器

// 功率增益校准寄存器
static constexpr uint8_t BL0906_WATTGN_1 = WATTGN_ADDR[0];
static constexpr uint8_t BL0906_WATTGN_2 = WATTGN_ADDR[1];
static constexpr uint8_t BL0906_WATTGN_3 = WATTGN_ADDR[2];
static constexpr uint8_t BL0906_WATTGN_4 = WATTGN_ADDR[3];
static constexpr uint8_t BL0906_WATTGN_5 = WATTGN_ADDR[4];
static constexpr uint8_t BL0906_WATTGN_6 = WATTGN_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_WATTGN_7 = WATTGN_ADDR[6];
static constexpr uint8_t BL0906_WATTGN_8 = WATTGN_ADDR[7];
static constexpr uint8_t BL0906_WATTGN_9 = WATTGN_ADDR[8];
static constexpr uint8_t BL0906_WATTGN_10 = WATTGN_ADDR[9];
#endif

// 功率偏置校准寄存器
static constexpr uint8_t BL0906_WATTOS_1 = WATTOS_ADDR[0];
static constexpr uint8_t BL0906_WATTOS_2 = WATTOS_ADDR[1];
static constexpr uint8_t BL0906_WATTOS_3 = WATTOS_ADDR[2];
static constexpr uint8_t BL0906_WATTOS_4 = WATTOS_ADDR[3];
static constexpr uint8_t BL0906_WATTOS_5 = WATTOS_ADDR[4];
static constexpr uint8_t BL0906_WATTOS_6 = WATTOS_ADDR[5];
#if CHANNEL_COUNT >= 10
static constexpr uint8_t BL0906_WATTOS_7 = WATTOS_ADDR[6];
static constexpr uint8_t BL0906_WATTOS_8 = WATTOS_ADDR[7];
static constexpr uint8_t BL0906_WATTOS_9 = WATTOS_ADDR[8];
static constexpr uint8_t BL0906_WATTOS_10 = WATTOS_ADDR[9];
#endif



} // namespace bl0906_factory
} // namespace esphome 