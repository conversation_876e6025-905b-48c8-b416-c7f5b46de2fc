# BL0906Factory 组件冗余代码分析报告

## 概述
本报告对 BL0906Factory 组件进行了全面的代码结构分析，识别出多处冗余代码和可简化的部分。该组件包含大量重复逻辑和不必要的复杂性，影响代码维护性和性能。

## 主要冗余问题

### 1. 寄存器类型判断函数重复实现

**问题描述：** 在多个文件中重复实现了相同的寄存器类型判断函数

**涉及文件：**
- `bl0906_factory.cpp` (lines 851, 868, 895)
- `uart_communication_adapter.cpp` (lines 413, 426)
- `spi_communication_adapter.cpp` (lines 414, 427)

**重复函数：**
```cpp
bool is_16bit_register(uint8_t address)
bool is_unsigned_register(uint8_t address) 
bool is_24bit_register(uint8_t address)
```

**简化建议：**
- 将这些函数移至 `bl0906_registers.h` 作为内联函数
- 或创建一个专门的寄存器工具类
- 消除在通信适配器中的重复实现

### 2. 状态机读取逻辑高度重复

**问题描述：** 在 `bl0906_factory.cpp` 的状态机中，各通道的读取逻辑几乎完全相同

**涉及代码：** 
- `State::READ_CHANNEL_1` 到 `State::READ_CHANNEL_6` (lines 176-250+)

**重复模式：**
```cpp
case State::READ_CHANNEL_X:
  {
    bool success = false;
    int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[X-1], &success);
    current_data_.channels[X-1].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
    
    current_data_.channels[X-1].power_raw = send_read_command_and_receive(BL0906_WATT[X-1], &success);
    if (!success) current_data_.channels[X-1].power_raw = 0;
    
    int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[X-1], &success);
    current_data_.channels[X-1].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
  }
```

**简化建议：**
- 创建一个 `read_channel_data(int channel)` 私有方法
- 用循环替代重复的状态机状态
- 减少状态机状态数量从当前的10+个到4-5个

### 3. 错误处理和统计更新重复

**问题描述：** 通信适配器中的错误处理和统计更新逻辑高度重复

**涉及文件：**
- `uart_communication_adapter.cpp`
- `spi_communication_adapter.cpp`

**重复模式：**
```cpp
// 错误设置
set_error(CommunicationError::TIMEOUT, "错误消息");
update_statistics(false, CommunicationError::TIMEOUT);
return 0/false;

// 成功处理
update_statistics(true);
return result;
```

**简化建议：**
- 创建统一的错误处理宏或方法
- 抽象重试机制到基类

### 4. 校准寄存器地址映射冗余

**问题描述：** 校准相关的寄存器地址映射逻辑散布在多个文件中

**涉及文件：**
- `bl0906_factory.h` (lines 324+)
- `bl0906_registers.h` (lines 104-162)
- `number.py` (lines 24-37)

**重复逻辑：**
- 寄存器地址到校准类型的映射
- 通道号到寄存器地址的转换
- 校准类型枚举在多处定义

**简化建议：**
- 统一所有校准相关映射到 `bl0906_registers.h`
- 使用模板或宏生成映射代码
- 简化Python配置的映射逻辑

### 5. 通信适配器接口冗余

**问题描述：** 两个通信适配器实现了大量相同的模板代码

**涉及文件：**
- `uart_communication_adapter.cpp`
- `spi_communication_adapter.cpp`

**重复功能：**
- 统计信息管理
- 错误状态管理
- 重试机制实现
- 状态查询方法

**简化建议：**
- 创建 `CommunicationAdapterBase` 基类
- 将通用功能移至基类
- 只在具体适配器中实现协议特定逻辑

### 6. 存储接口重复逻辑

**问题描述：** 两种存储实现有相似的错误处理和数据验证逻辑

**涉及文件：**
- `i2c_eeprom_calibration_storage.cpp`
- `preference_calibration_storage.cpp`

**重复模式：**
- 实例ID验证
- 数据序列化/反序列化
- 错误日志格式

**简化建议：**
- 抽象通用的存储逻辑到基类
- 统一数据格式和验证规则

### 7. Python配置映射冗余

**问题描述：** Python配置文件中有大量重复的映射和验证逻辑

**涉及文件：**
- `__init__.py` (lines 47-116)
- `sensor.py` (lines 20-200)
- `number.py` (lines 24-115)

**重复内容：**
- 传感器类型枚举映射
- 设备类别和单位映射
- 校准类型配置

**简化建议：**
- 创建共享的配置字典
- 使用配置生成器模式
- 减少硬编码映射

## 数据结构冗余

### 1. 传感器数组冗余

**问题：** 在 `bl0906_factory.h` 中定义了多个并行的传感器指针数组

```cpp
sensor::Sensor *current_sensors_[CHANNEL_COUNT];
sensor::Sensor *power_sensors_[CHANNEL_COUNT];
sensor::Sensor *energy_sensors_[CHANNEL_COUNT];
sensor::Sensor *total_energy_sensors_[CHANNEL_COUNT];
// ... 更多数组
```

**简化建议：**
- 使用结构体封装单个通道的所有传感器
- 创建传感器注册表替代多个数组

### 2. 校准数据结构重复

**问题：** 校准相关的数据结构在多处定义

**简化建议：**
- 统一校准数据结构定义
- 使用单一的校准管理器

## 逻辑冗余

### 1. 数据转换重复

**问题：** 原始数据到实际值的转换逻辑在多处重复

**涉及：**
- 有符号/无符号转换
- 单位转换
- 范围检查

**简化建议：**
- 创建统一的数据转换工具类
- 使用模板减少类型转换代码

### 2. 线程安全处理重复

**问题：** 多处使用相似的互斥锁模式

**简化建议：**
- 使用 RAII 锁管理器
- 抽象锁操作到工具函数

## 性能影响

### 1. 过多的状态机状态

**当前状态：** 10+ 个状态机状态处理6个通道的读取
**影响：** 增加了代码复杂度和执行开销
**建议：** 合并相似状态，使用循环处理

### 2. 重复的寄存器查找

**问题：** 每次寄存器操作都要进行类型判断
**建议：** 预计算寄存器属性表

### 3. 频繁的动态内存分配

**问题：** 字符串拼接和临时对象创建
**建议：** 使用对象池和字符串缓冲区

## 维护性问题

### 1. 硬编码数值散布

**问题：** 魔数（如通道数6、超时时间等）在多处硬编码
**建议：** 集中到常量定义文件

### 2. 注释和文档不一致

**问题：** 相似功能的注释在不同文件中表述不一致
**建议：** 标准化注释模板

### 3. 错误信息重复

**问题：** 相似的错误场景使用不同的错误消息
**建议：** 统一错误消息库

## 重构优先级建议

### 高优先级（立即处理）
1. **寄存器类型判断函数重复** - 影响编译时间和代码大小
2. **状态机读取逻辑重复** - 影响运行时性能
3. **通信适配器重复代码** - 影响维护性

### 中优先级（近期处理）
1. **存储接口重复逻辑** - 影响存储功能可靠性
2. **Python配置映射冗余** - 影响配置灵活性
3. **数据结构冗余** - 影响内存使用

### 低优先级（长期优化）
1. **错误处理统一化** - 提升用户体验
2. **注释和文档标准化** - 提升开发效率
3. **性能优化** - 提升运行效率

## 总结

BL0906Factory 组件虽然功能丰富，但存在严重的代码冗余问题。主要表现在：

1. **大量重复的函数实现**（特别是寄存器类型判断）
2. **冗余的状态机逻辑**（6个几乎相同的通道读取状态）
3. **重复的错误处理模式**（在所有通信适配器中）
4. **散布的配置映射逻辑**（Python和C++代码中）

通过系统性重构，可以：
- **减少代码量** 约30-40%
- **提升编译速度** 约20-30%
- **改善维护性** 显著降低修改成本
- **增强可读性** 便于新开发者理解

建议按照优先级逐步进行重构，每次重构后都要进行充分的测试验证。 

## 数据结构冗余重构计划

基于前面的分析，本节详细规划如何解决BL0906Factory组件中的数据结构冗余问题。重构将分阶段进行，确保每个阶段都能独立验证和测试。

### 第一阶段：传感器数组结构重构

#### 1.1 问题分析

**当前冗余情况：**
```cpp
// bl0906_factory.h - 主类中的并行传感器数组
sensor::Sensor *current_sensors_[CHANNEL_COUNT]{nullptr};     // 电流传感器
sensor::Sensor *power_sensors_[CHANNEL_COUNT]{nullptr};       // 功率传感器  
sensor::Sensor *energy_sensors_[CHANNEL_COUNT]{nullptr};      // 电量传感器
sensor::Sensor *total_energy_sensors_[CHANNEL_COUNT]{nullptr}; // 累计电量传感器

// energy_statistics_manager.h - 统计管理器中的并行传感器数组  
sensor::Sensor *yesterday_energy_sensors_[7]{nullptr};        // 昨日电量传感器
sensor::Sensor *today_energy_sensors_[7]{nullptr};           // 今日电量传感器
sensor::Sensor *week_energy_sensors_[7]{nullptr};            // 本周电量传感器
sensor::Sensor *month_energy_sensors_[7]{nullptr};           // 本月电量传感器
sensor::Sensor *year_energy_sensors_[7]{nullptr};            // 本年电量传感器
```

**冗余问题：**
1. **数据结构重复** - 相同的通道概念用多个数组表示
2. **索引管理复杂** - 需要同步维护多个并行数组的索引
3. **内存浪费** - 固定数组大小，实际使用可能更少
4. **访问逻辑重复** - 每种传感器类型都有相似的get/set方法
5. **维护困难** - 新增传感器类型需要修改多处代码

#### 1.2 重构目标

1. **统一传感器管理** - 用单一结构管理所有通道传感器
2. **类型安全访问** - 提供类型安全的传感器访问接口
3. **动态内存管理** - 根据实际需要分配内存
4. **简化接口** - 统一传感器注册和访问逻辑
5. **向后兼容** - 保持现有API的兼容性

#### 1.3 重构方案

**步骤1：创建统一的通道传感器结构**
```cpp
// 新的文件：sensor_container.h
namespace esphome {
namespace bl0906_factory {

// 单个通道的传感器集合
struct ChannelSensorSet {
    sensor::Sensor *current{nullptr};          // 电流传感器
    sensor::Sensor *power{nullptr};            // 功率传感器
    sensor::Sensor *energy{nullptr};           // 电量传感器
    sensor::Sensor *total_energy{nullptr};     // 累计电量传感器
    
    // 统计传感器
    sensor::Sensor *yesterday_energy{nullptr}; // 昨日电量
    sensor::Sensor *today_energy{nullptr};     // 今日电量
    sensor::Sensor *week_energy{nullptr};      // 本周电量
    sensor::Sensor *month_energy{nullptr};     // 本月电量
    sensor::Sensor *year_energy{nullptr};      // 本年电量
};

// 全局传感器集合（包含6个通道+总和）
struct GlobalSensorSet {
    // 基础传感器（全局唯一）
    sensor::Sensor *voltage{nullptr};          // 电压传感器
    sensor::Sensor *frequency{nullptr};        // 频率传感器
    sensor::Sensor *temperature{nullptr};      // 温度传感器
    
    // 总和传感器
    sensor::Sensor *power_sum{nullptr};        // 总功率
    sensor::Sensor *energy_sum{nullptr};       // 总电量
    sensor::Sensor *total_energy_sum{nullptr}; // 总累计电量
    
    // 总和统计传感器
    sensor::Sensor *sum_yesterday_energy{nullptr}; // 总昨日电量
    sensor::Sensor *sum_today_energy{nullptr};     // 总今日电量
    sensor::Sensor *sum_week_energy{nullptr};      // 总本周电量
    sensor::Sensor *sum_month_energy{nullptr};     // 总本月电量
    sensor::Sensor *sum_year_energy{nullptr};      // 总本年电量
};

// 统一的传感器容器
class SensorContainer {
public:
    SensorContainer() : channels_(6) {}  // 初始化6个通道
    
    // 通道传感器访问
    ChannelSensorSet& get_channel(int channel) { 
        return channels_.at(channel); 
    }
    const ChannelSensorSet& get_channel(int channel) const { 
        return channels_.at(channel); 
    }
    
    // 全局传感器访问
    GlobalSensorSet& get_global() { return global_; }
    const GlobalSensorSet& get_global() const { return global_; }
    
    // 类型安全的传感器设置方法
    void set_channel_sensor(int channel, SensorType type, sensor::Sensor *sensor);
    void set_global_sensor(SensorType type, sensor::Sensor *sensor);
    void set_statistics_sensor(int channel, StatisticsSensorType type, sensor::Sensor *sensor);
    
    // 类型安全的传感器获取方法
    sensor::Sensor* get_channel_sensor(int channel, SensorType type) const;
    sensor::Sensor* get_global_sensor(SensorType type) const;
    sensor::Sensor* get_statistics_sensor(int channel, StatisticsSensorType type) const;
    
    // 兼容性接口（逐步废弃）
    sensor::Sensor* get_sensor(SensorType type, int channel = 0) const;
    void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0);
    
private:
    std::vector<ChannelSensorSet> channels_;  // 6个通道
    GlobalSensorSet global_;                  // 全局传感器
};

}  // namespace bl0906_factory
}  // namespace esphome
```

**步骤2：修改BL0906Factory类使用新的传感器容器**
```cpp
// bl0906_factory.h 中的修改
class BL0906Factory : public PollingComponent, public Component {
private:
    // 移除旧的传感器数组
    // sensor::Sensor *current_sensors_[CHANNEL_COUNT]{nullptr};  // 删除
    // sensor::Sensor *power_sensors_[CHANNEL_COUNT]{nullptr};    // 删除
    // sensor::Sensor *energy_sensors_[CHANNEL_COUNT]{nullptr};   // 删除
    // sensor::Sensor *total_energy_sensors_[CHANNEL_COUNT]{nullptr}; // 删除
    
    // 新的统一传感器容器
    SensorContainer sensor_container_;
    
public:
    // 新的类型安全接口
    void set_channel_sensor(int channel, SensorType type, sensor::Sensor *sensor) {
        sensor_container_.set_channel_sensor(channel, type, sensor);
    }
    
    sensor::Sensor* get_channel_sensor(int channel, SensorType type) const {
        return sensor_container_.get_channel_sensor(channel, type);
    }
    
    void set_statistics_sensor(int channel, StatisticsSensorType type, sensor::Sensor *sensor) {
        sensor_container_.set_statistics_sensor(channel, type, sensor);
    }
    
    // 保持兼容性的旧接口（标记为废弃）
    [[deprecated("Use set_channel_sensor instead")]]
    void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0) {
        sensor_container_.set_sensor(type, sensor, channel);
    }
    
    [[deprecated("Use get_channel_sensor instead")]]
    sensor::Sensor* get_sensor(SensorType type, int channel = 0) const {
        return sensor_container_.get_sensor(type, channel);
    }
};
```

**步骤3：修改EnergyStatisticsManager使用新的传感器容器**
```cpp
// energy_statistics_manager.h 中的修改
class EnergyStatisticsManager : public PollingComponent {
private:
    // 移除旧的传感器数组
    // sensor::Sensor *yesterday_energy_sensors_[7]{nullptr};  // 删除
    // sensor::Sensor *today_energy_sensors_[7]{nullptr};     // 删除
    // sensor::Sensor *week_energy_sensors_[7]{nullptr};      // 删除
    // sensor::Sensor *month_energy_sensors_[7]{nullptr};     // 删除
    // sensor::Sensor *year_energy_sensors_[7]{nullptr};      // 删除
    
    // 使用父类的传感器容器
    BL0906Factory* parent_;
    
public:
    // 通过父类访问传感器
    void set_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel = 0) {
        parent_->set_statistics_sensor(channel, type, sensor);
    }
    
private:
    // 内部使用父类的传感器容器访问传感器
    sensor::Sensor* get_statistics_sensor(int channel, StatisticsSensorType type) const {
        return parent_->sensor_container_.get_statistics_sensor(channel, type);
    }
};
```

#### 1.4 实施计划

**阶段1.1：创建传感器容器基础结构**
- [ ] 创建 `sensor_container.h` 和 `sensor_container.cpp`
- [ ] 定义 `ChannelSensorSet` 和 `GlobalSensorSet` 结构
- [ ] 实现 `SensorContainer` 类的基本功能
- [ ] 编写单元测试验证基础功能

**阶段1.2：集成到BL0906Factory**
- [ ] 修改 `bl0906_factory.h` 使用新的传感器容器
- [ ] 更新所有传感器访问逻辑
- [ ] 保持旧接口的兼容性（标记为废弃）
- [ ] 运行集成测试验证功能正确性

**阶段1.3：更新EnergyStatisticsManager**
- [ ] 修改 `energy_statistics_manager.h` 使用父类的传感器容器
- [ ] 移除重复的传感器数组
- [ ] 更新所有传感器访问逻辑
- [ ] 验证统计功能正常工作

**阶段1.4：清理和优化**
- [ ] 移除标记为废弃的旧接口
- [ ] 更新文档和注释
- [ ] 性能测试和优化
- [ ] 最终集成测试

### 第二阶段：校准数据结构统一

#### 2.1 问题分析

**当前冗余情况：**
```cpp
// bl0906_factory.h 中的枚举
enum class CalibRegType {
    CHGN, CHOS, RMSGN, RMSOS, WATTGN, WATTOS, CHGN_V, CHOS_V
};

enum class CalibNumberType {  
    CURRENT_GAIN, CURRENT_OFFSET, VOLTAGE_GAIN, VOLTAGE_OFFSET,
    POWER_GAIN, POWER_OFFSET, FREQUENCY_GAIN, FREQUENCY_OFFSET
};

// calibration_storage_interface.h 中的结构
struct CalibrationEntry {
    uint8_t register_addr;
    int16_t value;
} __attribute__((packed));

// config_mappings.py 中的映射（Python端）
CALIB_REG_MAPPING = {
    'current_gain': ..., 
    'current_offset': ...,
    'voltage_gain': ...,
    # ... 更多映射
}
```

**冗余问题：**
1. **类型定义分散** - 校准类型在多个文件中重复定义
2. **映射逻辑重复** - C++和Python都有类似的映射逻辑
3. **数据结构不统一** - 不同地方使用不同的数据结构表示相同概念
4. **维护困难** - 新增校准类型需要修改多个文件
5. **类型安全性差** - 缺乏统一的类型检查机制

#### 2.2 重构目标

1. **统一校准类型定义** - 所有校准类型在一个地方定义
2. **统一数据结构** - 使用一致的数据结构表示校准数据
3. **类型安全访问** - 提供类型安全的校准数据访问接口
4. **自动映射生成** - 通过代码生成减少手工映射
5. **向后兼容** - 保持现有配置文件的兼容性

#### 2.3 重构方案

**步骤1：创建统一的校准类型定义**
```cpp
// 新的文件：calibration_types.h
namespace esphome {
namespace bl0906_factory {

// 统一的校准类型枚举（替代CalibRegType和CalibNumberType）
enum class CalibrationType : uint8_t {
    // 通道相关校准（需要通道号）
    CURRENT_GAIN = 0,      // 电流增益 (CHGN)
    CURRENT_OFFSET,        // 电流偏移 (CHOS)
    RMS_GAIN,             // RMS增益 (RMSGN)
    RMS_OFFSET,           // RMS偏移 (RMSOS)
    POWER_GAIN,           // 功率增益 (WATTGN)  
    POWER_OFFSET,         // 功率偏移 (WATTOS)
    
    // 全局校准（不需要通道号）
    VOLTAGE_GAIN,         // 电压增益 (CHGN_V)
    VOLTAGE_OFFSET,       // 电压偏移 (CHOS_V)
    
    // 元数据
    TYPE_COUNT            // 总类型数
};

// 校准类型属性
struct CalibrationTypeInfo {
    CalibrationType type;
    const char* name;           // 人可读的名称
    const char* config_key;     // YAML配置键名
    bool requires_channel;      // 是否需要通道号
    uint8_t base_register;      // 基础寄存器地址
    int16_t default_value;      // 默认值
    int16_t min_value;          // 最小值
    int16_t max_value;          // 最大值
};

// 校准类型信息表（编译时常量）
constexpr CalibrationTypeInfo CALIBRATION_TYPE_INFO[] = {
    {CalibrationType::CURRENT_GAIN,   "Current Gain",   "current_gain",   true,  BL0906_CHGN[0],   0x8000, 0x0000, 0xFFFF},
    {CalibrationType::CURRENT_OFFSET, "Current Offset", "current_offset", true,  BL0906_CHOS[0],   0x0000, -32768, 32767},
    {CalibrationType::RMS_GAIN,       "RMS Gain",       "rms_gain",       true,  BL0906_RMSGN[0],  0x8000, 0x0000, 0xFFFF},
    {CalibrationType::RMS_OFFSET,     "RMS Offset",     "rms_offset",     true,  BL0906_RMSOS[0],  0x0000, -32768, 32767},
    {CalibrationType::POWER_GAIN,     "Power Gain",     "power_gain",     true,  BL0906_WATTGN[0], 0x8000, 0x0000, 0xFFFF},
    {CalibrationType::POWER_OFFSET,   "Power Offset",   "power_offset",   true,  BL0906_WATTOS[0], 0x0000, -32768, 32767},
    {CalibrationType::VOLTAGE_GAIN,   "Voltage Gain",   "voltage_gain",   false, BL0906_CHGN_V,    0x8000, 0x0000, 0xFFFF},
    {CalibrationType::VOLTAGE_OFFSET, "Voltage Offset", "voltage_offset", false, BL0906_CHOS_V,    0x0000, -32768, 32767},
};

// 校准数据条目（统一结构）
struct CalibrationEntry {
    CalibrationType type;       // 校准类型
    int8_t channel;            // 通道号（-1表示全局）
    int16_t value;             // 校准值
    uint32_t timestamp;        // 设置时间戳
    
    // 获取寄存器地址
    uint8_t get_register_address() const;
    
    // 验证数据有效性
    bool is_valid() const;
    
    // 序列化支持
    uint32_t calculate_checksum() const;
} __attribute__((packed));

// 校准数据集合
struct CalibrationDataSet {
    std::vector<CalibrationEntry> entries;
    uint32_t checksum;
    uint32_t version;
    
    // 数据操作方法
    void add_entry(CalibrationType type, int8_t channel, int16_t value);
    bool get_entry(CalibrationType type, int8_t channel, int16_t& value) const;
    void remove_entry(CalibrationType type, int8_t channel);
    
    // 数据验证
    bool verify_checksum() const;
    void update_checksum();
    
    // 序列化支持
    std::vector<uint8_t> serialize() const;
    bool deserialize(const std::vector<uint8_t>& data);
};

// 工具函数
constexpr const CalibrationTypeInfo& get_calibration_type_info(CalibrationType type) {
    return CALIBRATION_TYPE_INFO[static_cast<uint8_t>(type)];
}

uint8_t get_register_address(CalibrationType type, int8_t channel = -1);
const char* get_config_key(CalibrationType type);
bool is_valid_calibration_value(CalibrationType type, int16_t value);

}  // namespace bl0906_factory  
}  // namespace esphome
```

**步骤2：创建统一的校准管理器**
```cpp
// 新的文件：calibration_manager.h
namespace esphome {
namespace bl0906_factory {

class CalibrationManager {
public:
    CalibrationManager(BL0906Factory* parent);
    
    // 校准数据操作
    bool set_calibration(CalibrationType type, int8_t channel, int16_t value);
    bool get_calibration(CalibrationType type, int8_t channel, int16_t& value) const;
    bool has_calibration(CalibrationType type, int8_t channel) const;
    
    // 批量操作
    bool load_calibration_set(const CalibrationDataSet& data_set);
    CalibrationDataSet save_calibration_set() const;
    void reset_all_calibrations();
    
    // 存储管理
    bool save_to_storage();
    bool load_from_storage();
    bool clear_storage();
    
    // Number组件集成
    void register_number_component(CalibrationType type, int8_t channel, number::Number* number);
    void update_all_number_components();
    
    // 验证和诊断
    bool validate_all_calibrations() const;
    void diagnose_calibration_status() const;
    
private:
    BL0906Factory* parent_;
    CalibrationDataSet current_data_;
    std::map<std::pair<CalibrationType, int8_t>, number::Number*> number_components_;
    
    // 内部方法
    bool write_to_hardware(const CalibrationEntry& entry);
    bool read_from_hardware(CalibrationEntry& entry);
    void notify_value_changed(CalibrationType type, int8_t channel, int16_t new_value);
};

}  // namespace bl0906_factory
}  // namespace esphome
```

**步骤3：更新Python配置映射**
```python
# config_mappings.py 中的更新
from enum import IntEnum

class CalibrationType(IntEnum):
    """与C++端保持一致的校准类型枚举"""
    CURRENT_GAIN = 0
    CURRENT_OFFSET = 1
    RMS_GAIN = 2
    RMS_OFFSET = 3
    POWER_GAIN = 4
    POWER_OFFSET = 5
    VOLTAGE_GAIN = 6
    VOLTAGE_OFFSET = 7

# 自动生成的配置映射（减少手工维护）
CALIBRATION_TYPE_INFO = {
    CalibrationType.CURRENT_GAIN: {
        'name': 'Current Gain',
        'config_key': 'current_gain', 
        'requires_channel': True,
        'default_value': 0x8000,
        'min_value': 0x0000,
        'max_value': 0xFFFF,
    },
    # ... 其他类型通过代码生成
}

def generate_calibration_config():
    """自动生成校准配置字典"""
    config = {}
    for calib_type, info in CALIBRATION_TYPE_INFO.items():
        if info['requires_channel']:
            # 生成通道相关的配置
            for channel in range(6):
                key = f"{info['config_key']}_ch{channel+1}"
                config[key] = cv.Optional(
                    cv.int_range(min=info['min_value'], max=info['max_value']),
                    default=info['default_value']
                )
        else:
            # 生成全局配置
            config[info['config_key']] = cv.Optional(
                cv.int_range(min=info['min_value'], max=info['max_value']),
                default=info['default_value']
            )
    return config

# 使用自动生成的配置
CALIBRATION_CONFIG_SCHEMA = generate_calibration_config()
```

#### 2.4 实施计划

**阶段2.1：创建统一的校准类型系统**
- [ ] 创建 `calibration_types.h` 和 `calibration_types.cpp`
- [ ] 定义统一的 `CalibrationType` 枚举和相关结构
- [ ] 实现类型信息查询和验证功能
- [ ] 编写单元测试验证类型系统

**阶段2.2：实现校准管理器**
- [ ] 创建 `calibration_manager.h` 和 `calibration_manager.cpp`
- [ ] 实现校准数据的增删改查功能
- [ ] 集成存储和Number组件支持
- [ ] 测试校准管理器的各项功能

**阶段2.3：更新BL0906Factory集成**
- [ ] 修改 `bl0906_factory.h` 使用新的校准管理器
- [ ] 移除旧的校准相关代码
- [ ] 更新所有校准访问逻辑
- [ ] 验证校准功能正常工作

**阶段2.4：更新Python配置系统**
- [ ] 修改 `config_mappings.py` 使用统一的类型定义
- [ ] 实现自动配置生成功能
- [ ] 更新 `__init__.py`、`sensor.py`、`number.py`
- [ ] 测试配置解析和生成功能

### 第三阶段：数据传输结构优化

#### 3.1 问题分析

**当前冗余情况：**
```cpp
// bl0906_factory.h 中的结构
struct DataPacket {
    uint8_t l; uint8_t m; uint8_t h; uint8_t checksum;
};

struct ube24_t {
    uint8_t l; uint8_t m; uint8_t h;
};

struct sbe24_t {
    uint8_t l; uint8_t m; int8_t h;
};

struct RawSensorData {
    uint32_t temperature_raw;
    uint32_t frequency_raw;
    uint32_t voltage_raw;
    struct ChannelData {
        uint32_t current_raw;
        int32_t power_raw;
        uint32_t energy_raw;
    } channels[6];
    int32_t power_sum_raw;
    uint32_t energy_sum_raw;
    uint32_t timestamp;
    bool read_complete;
};
```

**冗余问题：**
1. **相似的24位数据结构** - `ube24_t` 和 `sbe24_t` 功能重叠
2. **数据包结构冗余** - `DataPacket` 和24位结构概念重复
3. **原始数据结构庞大** - `RawSensorData` 包含大量冗余字段
4. **类型转换复杂** - 多种数据结构间的转换逻辑复杂

#### 3.2 重构方案

**步骤1：统一数据类型定义**
```cpp
// 新的文件：data_types.h
namespace esphome {
namespace bl0906_factory {

// 统一的24位数据类型（支持有符号和无符号）
template<bool Signed = false>
struct int24_t {
    uint8_t bytes[3];  // 小端序存储
    
    // 构造函数
    int24_t() { bytes[0] = bytes[1] = bytes[2] = 0; }
    int24_t(int32_t value) { set_value(value); }
    
    // 值操作
    int32_t get_value() const;
    void set_value(int32_t value);
    
    // 操作符重载
    operator int32_t() const { return get_value(); }
    int24_t& operator=(int32_t value) { set_value(value); return *this; }
    
    // 字节访问
    uint8_t low() const { return bytes[0]; }
    uint8_t mid() const { return bytes[1]; }
    uint8_t high() const { return bytes[2]; }
} __attribute__((packed));

using uint24_t = int24_t<false>;  // 无符号24位
using int24_s_t = int24_t<true>;  // 有符号24位

// 通用数据包结构
struct DataPacket {
    uint24_t data;       // 24位数据
    uint8_t checksum;    // 校验和
    
    bool is_valid() const;
    static uint8_t calculate_checksum(const uint24_t& data);
} __attribute__((packed));

// 紧凑的传感器数据结构
struct CompactSensorData {
    // 基础数据（12字节）
    uint24_t voltage_raw;      // 电压
    uint24_t frequency_raw;    // 频率  
    uint24_t temperature_raw;  // 温度
    uint24_t power_sum_raw;    // 总功率
    
    // 通道数据（72字节：6通道 × 12字节）
    struct {
        uint24_t current_raw;   // 电流
        int24_s_t power_raw;    // 功率（有符号）
        uint24_t energy_raw;    // 电量
    } channels[6];
    
    // 元数据（8字节）
    uint32_t timestamp;        // 时间戳
    uint16_t sequence;         // 序列号
    uint8_t flags;            // 标志位
    uint8_t checksum;         // 数据校验和
    
    // 总计：92字节（比原来的RawSensorData小约30%）
    
    // 数据操作方法
    bool is_valid() const;
    void update_checksum();
    void set_timestamp();
} __attribute__((packed));

}  // namespace bl0906_factory
}  // namespace esphome
```

#### 3.3 实施计划

**阶段3.1：创建统一数据类型**
- [ ] 创建 `data_types.h` 和 `data_types.cpp`
- [ ] 实现模板化的24位整数类型
- [ ] 实现紧凑的数据包和传感器数据结构
- [ ] 编写单元测试验证数据类型功能

**阶段3.2：集成到通信适配器**
- [ ] 修改通信适配器使用新的数据类型
- [ ] 更新数据序列化和反序列化逻辑
- [ ] 测试通信功能的正确性
- [ ] 性能测试和优化

**阶段3.3：更新主类数据结构**
- [ ] 修改 `BL0906Factory` 使用 `CompactSensorData`
- [ ] 更新所有数据访问和处理逻辑
- [ ] 移除旧的数据结构定义
- [ ] 验证功能完整性

### 第四阶段：完整性验证和清理

#### 4.1 完整性测试

**功能测试清单：**
- [ ] 传感器数据读取和发布功能
- [ ] 校准数据设置和读取功能
- [ ] 电量统计和持久化功能
- [ ] 通信适配器切换功能
- [ ] Python配置解析功能
- [ ] Number组件交互功能

**性能测试清单：**
- [ ] 内存使用量对比（重构前后）
- [ ] CPU使用率对比（重构前后）
- [ ] 编译时间对比（重构前后）
- [ ] 运行时性能对比（重构前后）

#### 4.2 代码清理

**清理任务清单：**
- [ ] 移除所有标记为废弃的旧接口
- [ ] 删除未使用的头文件和源文件
- [ ] 整理和标准化注释格式
- [ ] 更新所有相关文档

#### 4.3 文档更新

**文档更新清单：**
- [ ] 更新README.md说明新的架构
- [ ] 更新API文档反映新的接口
- [ ] 创建重构迁移指南
- [ ] 更新配置示例和最佳实践

### 第五阶段：预期收益评估

#### 5.1 代码质量提升

**量化指标：**
- **代码行数减少**：预计减少25-30%（约400-500行）
- **重复代码率**：从当前35%降至5%以下
- **圈复杂度**：平均降低40%
- **文件数量**：合并冗余文件，减少10-15个文件

#### 5.2 维护性改善

**改善方面：**
- **单一职责**：每个类和结构只负责一种类型的数据
- **类型安全**：编译时类型检查，减少运行时错误
- **接口一致**：统一的访问模式，降低学习成本
- **扩展便利**：新增传感器类型只需修改一处配置

#### 5.3 性能优化

**优化效果：**
- **内存使用**：数据结构优化预计节省20-25%内存
- **编译时间**：减少模板实例化和包含依赖，提升15-20%
- **运行时性能**：缓存友好的数据布局，提升5-10%

#### 5.4 用户体验提升

**体验改善：**
- **配置简化**：自动生成的配置减少用户配置错误
- **错误诊断**：统一的错误处理提供更好的错误信息
- **功能一致**：所有传感器类型使用相同的操作模式

### 重构风险评估和缓解措施

#### 风险评估

**高风险项：**
1. **向后兼容性破坏** - 可能影响现有用户配置
2. **数据格式变更** - 可能导致存储的校准数据丢失
3. **接口变更** - 可能影响依赖的外部代码

**中等风险项：**
1. **性能回归** - 新结构可能引入性能问题
2. **功能缺失** - 重构过程中可能遗漏某些功能
3. **测试覆盖不足** - 新代码的测试可能不够充分

#### 缓解措施

**兼容性保护：**
- 保留旧接口并标记为废弃，逐步迁移
- 提供自动配置迁移工具
- 支持多版本数据格式共存

**质量保证：**
- 每个阶段都有完整的测试覆盖
- 代码审查和性能基准测试
- 渐进式发布，允许回滚

**用户支持：**
- 详细的迁移文档和示例
- 社区支持和问题反馈渠道
- 向后兼容性支持至少保持2个主版本

这个重构计划将分5个阶段实施，每个阶段都有明确的目标、实施步骤和验证标准。通过系统性的重构，预期将显著提升代码质量、维护性和性能。 