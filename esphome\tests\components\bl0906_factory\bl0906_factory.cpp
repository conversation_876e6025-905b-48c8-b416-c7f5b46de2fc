#include "bl0906_factory.h"
#include "bl0906_number.h"
#include "energy_statistics_manager.h"
#include "adapter_registry.h"

// 调试宏定义状态（编译时显示）
#ifdef USE_SPI_COMMUNICATION_ADAPTER
#pragma message("BL0906Factory: SPI communication adapter enabled")
#endif

#ifdef USE_I2C_EEPROM_CALIBRATION
#pragma message("BL0906Factory: I2C EEPROM calibration storage enabled")
#endif

#ifdef USE_UART_COMMUNICATION_ADAPTER
#pragma message("BL0906Factory: UART communication adapter enabled")
#endif

// 根据配置包含相应的通信适配器
#ifdef USE_UART_COMMUNICATION_ADAPTER
#include "uart_communication_adapter.h"
#endif

#ifdef USE_SPI_COMMUNICATION_ADAPTER
#include "spi_communication_adapter.h"
#endif

#include <map>
#include <string>
#include <cstring>  // for memcpy
#include <cmath>    // for abs
#include <memory>   // for std::unique_ptr
#include "esphome/core/hal.h"
#include "esphome/core/application.h"
#include "esphome/core/time.h"
#include "esphome/components/time/real_time_clock.h"

// 避免命名空间冲突
namespace esphome_time = esphome::time;

/*
 * 重要说明：
 * 此组件继承了uart::UARTDevice，因此不需要使用parent_指针。
 * 在ESPHome中，如果组件继承了UARTDevice，它已经可以直接使用this->write_byte()等方法进行通信。
 * 因此，不需要调用set_parent方法或使用parent_指针。
 * 本次修改将所有使用parent_指针的地方改为使用继承的UART方法（this->write_byte等）
 */

namespace esphome {
namespace bl0906_factory {

static const char *const FACTORY_TAG = "bl0906_factory";  // 修改日志标签
const char *const BL0906Factory::BL0906_FACTORY_ID = "bl0906_factory";
// 定义静态实例指针 - 修改：指向 BL0906Factory 类型
BL0906Factory *BL0906Factory::bl0906_instance = nullptr;

// 旧的写保护命令数组已被适配器中的动态命令生成替代

// 添加校验和函数定义
uint8_t bl0906_checksum(const uint8_t address, const DataPacket *data) {
  uint8_t sum = address;
  sum += data->l;
  sum += data->m;
  sum += data->h;
  return sum ^ 0xFF;
}
BL0906Factory::BL0906Factory() {}

// ========== 通信适配器接口实现 ==========

void BL0906Factory::set_communication_adapter(std::unique_ptr<CommunicationAdapterInterface> adapter) {
  comm_adapter_ = std::move(adapter);
  ESP_LOGI(FACTORY_TAG, "设置通信适配器: %s", 
           comm_adapter_ ? comm_adapter_->get_adapter_type().c_str() : "null");
}

CommunicationAdapterInterface* BL0906Factory::get_communication_adapter() const {
  return comm_adapter_.get();
}

// 统一的寄存器读取函数（现在通过适配器实现）
// 这是唯一的数据读取函数，所有数据读取都通过此函数
// 修改：添加success参数来区分读取失败和寄存器值为0的情况
int32_t BL0906Factory::send_read_command_and_receive(uint8_t address, bool* success) {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    if (success) *success = false;
    return 0;
  }

  return comm_adapter_->read_register(address, success);
}



// 统一的数据转换函数（根据寄存器地址和原始值转换为实际值）
// 这是唯一的数据转换函数，所有数据转换都通过此函数
// 修改为支持条件编译的动态地址判断
float BL0906Factory::convert_raw_to_value(uint8_t address, int32_t raw_value) {
  // 基础传感器寄存器检查
  if (address == FREQUENCY_ADDR) {
    // 频率是无符号数，确保使用无符号计算
    return (raw_value > 0) ? 10000000.0f / static_cast<uint32_t>(raw_value) : 0;
  }
  
  if (address == TEMPERATURE_ADDR) {
    // 温度是无符号数
    uint32_t unsigned_raw = static_cast<uint32_t>(raw_value);
    float temp_celsius = (unsigned_raw - 64) * 12.5f / 59.0f - 40.0f;
    ESP_LOGV(FACTORY_TAG, "温度转换: 原始值=%d, 无符号值=%u, 计算结果=%.2f°C",
             raw_value, unsigned_raw, temp_celsius);
    return temp_celsius;
  }
  
  if (address == V_RMS_ADDR) {
    // 电压是无符号数
    return static_cast<uint32_t>(raw_value) / Kv;
  }
  
  if (address == WATT_SUM_ADDR) {
    return raw_value / Kp_sum;  // 保持有符号
  }
  
  if (address == CF_SUM_ADDR) {
    return static_cast<uint32_t>(raw_value) / Ke_sum;
  }
  
  // 动态检查通道寄存器
  for (int i = 0; i < MAX_CHANNELS; i++) {
    if (address == I_RMS_ADDR[i]) {
      return static_cast<uint32_t>(raw_value) / Ki;
    }
    if (address == WATT_ADDR[i]) {
      return raw_value / Kp;  // 保持有符号
    }
    if (address == CF_CNT_ADDR[i]) {
      return static_cast<uint32_t>(raw_value) / Ke;
    }
  }
  
  // 检查MODE2寄存器（如果存在）
  if (address == BL0906_MODE2) {
    // MODE2寄存器直接返回原始值（用于位操作）
    return static_cast<uint32_t>(raw_value);
  }
  
  ESP_LOGW(FACTORY_TAG, "未知寄存器地址: 0x%02X", address);
  return raw_value;  // 未知寄存器直接返回原始值
}

// 重构后的状态机 - 数据读取和处理完全分离
void BL0906Factory::loop() {
  // 通信适配器会处理缓冲区管理，无需条件编译

  switch (this->current_state_) {
    case State::IDLE:
      // 初始化新的数据读取周期
      current_data_ = {};
      current_data_.timestamp = millis();
      data_collection_complete_ = false;
      data_read_start_time_ = millis();
      this->current_state_ = State::READ_BASIC_SENSORS;
      break;

    case State::READ_BASIC_SENSORS:
      // 直接调用统一读取函数读取基础传感器数据（使用条件编译的地址）
      {
        bool success = false;
        int32_t temp_raw = send_read_command_and_receive(TEMPERATURE_ADDR, &success);
        current_data_.temperature_raw = success ? static_cast<uint32_t>(temp_raw) : 0;
        
        
        int32_t freq_raw = send_read_command_and_receive(FREQUENCY_ADDR, &success);
        current_data_.frequency_raw = success ? static_cast<uint32_t>(freq_raw) : 0;
        
        int32_t volt_raw = send_read_command_and_receive(V_RMS_ADDR, &success);
        current_data_.voltage_raw = success ? static_cast<uint32_t>(volt_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNELS;
      break;

    case State::READ_CHANNELS:
      // 重构：一次性读取所有通道数据
      if (read_all_channels_data()) {
        this->current_state_ = State::READ_TOTAL_DATA;
      } else {
        // 错误处理：可选择重试或跳过
        ESP_LOGW(FACTORY_TAG, "通道数据读取失败，跳过此周期");
        this->current_state_ = State::READ_TOTAL_DATA;  // 继续后续流程
      }
      break;



    case State::READ_TOTAL_DATA:
      // 直接调用统一读取函数读取总和数据（使用条件编译的地址）
      {
        bool success = false;
        current_data_.power_sum_raw = send_read_command_and_receive(WATT_SUM_ADDR, &success);
        if (!success) current_data_.power_sum_raw = 0;
        
        int32_t energy_sum_raw = send_read_command_and_receive(CF_SUM_ADDR, &success);
        current_data_.energy_sum_raw = success ? static_cast<uint32_t>(energy_sum_raw) : 0;
      }
      current_data_.read_complete = true;
      data_collection_complete_ = true;
      this->current_state_ = State::CHECK_CHIP_RESTART;
      break;

    case State::CHECK_CHIP_RESTART:
      detect_chip_restart(current_data_);
      this->current_state_ = State::PROCESS_PERSISTENCE;
      break;

    case State::PROCESS_PERSISTENCE:
      process_energy_persistence(current_data_);
      this->current_state_ = State::UPDATE_STATISTICS;
      break;

    case State::UPDATE_STATISTICS:
      update_energy_statistics(current_data_);
      this->current_state_ = State::PUBLISH_SENSORS;
      break;

    case State::PUBLISH_SENSORS:
      publish_all_sensors(current_data_);
      this->current_state_ = State::HANDLE_ACTIONS;
      break;

    case State::HANDLE_ACTIONS:
      this->handle_actions_();
      break;
  }
}



// 芯片重启检测（基于完整数据集）- 优化版本，避免重复检测
void BL0906Factory::detect_chip_restart(const RawSensorData& data) {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过芯片重启检测");
    return;
  }

  // 添加超时机制，防止重启检测永远不恢复（5分钟超时）
  static const uint32_t RESTART_DETECTION_TIMEOUT = 300000;  // 5分钟超时
  if (chip_restart_detected_ && 
      (millis() - last_restart_detection_time_) > RESTART_DETECTION_TIMEOUT) {
    ESP_LOGW(FACTORY_TAG, "芯片重启检测超时，强制重新启用检测");
    chip_restart_detected_ = false;
  }

  ESP_LOGV(FACTORY_TAG, "开始检测BL0906芯片重启...");

  bool all_channels_low = true;  // 标记所有通道是否都小于5

  // 检查各通道的CF_count
  for (int i = 0; i < MAX_CHANNELS; i++) {
    uint32_t hardware_cf_count = data.channels[i].energy_raw;
    
    ESP_LOGV(FACTORY_TAG, "通道%d硬件CF_count: %u", i+1, hardware_cf_count);

    // 检查是否小于5
    if (hardware_cf_count >= 50) {
      all_channels_low = false;
      ESP_LOGV(FACTORY_TAG, "通道%d CF_count=%u >= 50，芯片未重启", i+1, hardware_cf_count);
    }
  }

  // 如果之前已检测到重启，检查是否可以重新启用检测
  if (chip_restart_detected_) {
    if (!all_channels_low) {
      // 条件不满足了，重新启用检测
      chip_restart_detected_ = false;
      ESP_LOGI(FACTORY_TAG, "芯片重启检测条件不再满足，重新启用重启检测");
    } else {
      // 仍然满足重启条件，跳过检测
      ESP_LOGV(FACTORY_TAG, "芯片重启已检测，跳过重复检测");
      return;
    }
  }

  // 只有所有通道的CF_count都小于50时才确定芯片重启
  if (all_channels_low && !chip_restart_detected_) {
    ESP_LOGW(FACTORY_TAG, "检测到BL0906芯片重启：所有通道CF_count都小于50");
    
    // 设置重启检测标记
    chip_restart_detected_ = true;
    last_restart_detection_time_ = millis();
    chip_restart_count_++;  // 递增重启次数计数器
    
    // 重新记录各通道的CF_count值
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      uint32_t hardware_cf_count = data.channels[i].energy_raw;
      ESP_LOGI(FACTORY_TAG, "通道%d重启时CF_count: %u", i+1, hardware_cf_count);
      last_cf_count_[i] = hardware_cf_count;
    }
    
    // 标记需要保存last_cf_count
    last_cf_count_needs_save_ = true;
    
    ESP_LOGI(FACTORY_TAG, "芯片重启检测完成，已更新基准CF_count值，暂停重启检测 (重启次数: %u)", chip_restart_count_);
  } else {
    ESP_LOGV(FACTORY_TAG, "芯片未重启，继续正常运行");
  }
}

// 持久化存储处理 - 修复版本
void BL0906Factory::process_energy_persistence(const RawSensorData& data) {
  if (!energy_persistence_enabled_) {
    return;
  }

  // 修复：正确的持久化逻辑
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t current_count = data.channels[i].energy_raw;
    
    // 正确的公式：persistent_cf_count += (current_count - last_cf_count)
    // 但要处理芯片重启的情况（current_count < last_cf_count）
    if (current_count >= last_cf_count_[i]) {
      // 正常情况：硬件计数器递增
      uint32_t increment = current_count - last_cf_count_[i];
      persistent_cf_count_[i] += increment;
      ESP_LOGD(FACTORY_TAG, "通道%d: 硬件CF_count=%u, last=%u, 增量=%u, 持久化=%u",
               i+1, current_count, last_cf_count_[i], increment, persistent_cf_count_[i]);
    } else {
      // 芯片重启情况：硬件计数器被重置，累计当前值
      ESP_LOGW(FACTORY_TAG, "通道%d检测到硬件计数器重置: 当前=%u < 上次=%u",
               i+1, current_count, last_cf_count_[i]);
      persistent_cf_count_[i] += current_count;  // 累计当前值
      ESP_LOGI(FACTORY_TAG, "通道%d重启后累计: +%u, 持久化总计=%u", 
               i+1, current_count, persistent_cf_count_[i]);
    }
    
    // 更新基准值
    last_cf_count_[i] = current_count;
  }

  // 处理总和（使用相同逻辑）
  uint32_t current_sum_count = data.energy_sum_raw;
  const int sum_index = 6;
  
  if (current_sum_count >= last_cf_count_[sum_index]) {
    uint32_t increment = current_sum_count - last_cf_count_[sum_index];
    persistent_cf_count_[sum_index] += increment;
    ESP_LOGD(FACTORY_TAG, "总和: 硬件CF_count=%u, last=%u, 增量=%u, 持久化=%u",
             current_sum_count, last_cf_count_[sum_index], increment, persistent_cf_count_[sum_index]);
  } else {
    ESP_LOGW(FACTORY_TAG, "总和检测到硬件计数器重置: 当前=%u < 上次=%u",
             current_sum_count, last_cf_count_[sum_index]);
    persistent_cf_count_[sum_index] += current_sum_count;
    ESP_LOGI(FACTORY_TAG, "总和重启后累计: +%u, 持久化总计=%u", 
             current_sum_count, persistent_cf_count_[sum_index]);
  }
  
  last_cf_count_[sum_index] = current_sum_count;

  // 标志位触发逻辑（芯片重启检测触发）
  if (last_cf_count_needs_save_) {
    ESP_LOGI(FACTORY_TAG, "芯片重启标志触发：保存当前持久化数据");
    
    // 执行保存操作
    save_energy_data();
    
    // 更新 saved_total_cf 为当前6通道CF总和
    saved_total_cf_ = 0;
    for (int j = 0; j < CHANNEL_COUNT; j++) {
      saved_total_cf_ += persistent_cf_count_[j];
    }
    ESP_LOGI(FACTORY_TAG, "更新saved_total_cf: %u", saved_total_cf_);
    
    // 重置标志位
    last_cf_count_needs_save_ = false;
  }

  // 总和校验触发逻辑 - 每累计1000个脉冲保存一次
  uint32_t current_total_cf = 0;
  for (int j = 0; j < CHANNEL_COUNT; j++) {
    current_total_cf += persistent_cf_count_[j];
  }

  if (current_total_cf >= saved_total_cf_ && 
      (current_total_cf - saved_total_cf_) >= 1000) {
    ESP_LOGI(FACTORY_TAG, "总和校验触发保存: 当前总和=%u, 上次保存=%u, 差值=%u", 
             current_total_cf, saved_total_cf_, current_total_cf - saved_total_cf_);
    
    save_energy_data();
    saved_total_cf_ = current_total_cf;
  }
}

// 能量统计更新
void BL0906Factory::update_energy_statistics(const RawSensorData& data) {
  if (!energy_statistics_enabled_ || !energy_stats_manager_) {
    return;
  }

  // 更新各通道的统计数据
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t current_count = data.channels[i].energy_raw;
    if (current_count >= last_cf_count_[i]) {
      uint32_t pulse_increment = current_count - last_cf_count_[i];
      if (pulse_increment > 0) {
        energy_stats_manager_->update_persistent_cf_count(i, pulse_increment);
      }
    }
  }

  // 更新总和统计数据
  uint32_t current_sum_count = data.energy_sum_raw;
  const int sum_index = 6;
  if (current_sum_count >= last_cf_count_[sum_index]) {
    uint32_t pulse_increment = current_sum_count - last_cf_count_[sum_index];
    if (pulse_increment > 0) {
      energy_stats_manager_->update_persistent_cf_count_sum(pulse_increment);
    }
  }
}

// 传感器数据发布
void BL0906Factory::publish_all_sensors(const RawSensorData& data) {
  // 发布基础传感器数据
  if (temperature_sensor_) {
    float value = convert_raw_to_value(BL0906_TEMPERATURE, data.temperature_raw);
    temperature_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "温度传感器发布: 原始值=%u, 计算值=%.2f°C", data.temperature_raw, value);
  }
  
  if (frequency_sensor_) {
    float value = convert_raw_to_value(BL0906_FREQUENCY, data.frequency_raw);
    frequency_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "频率传感器: 原始值=%u, 计算值=%.2f", data.frequency_raw, value);
  }
  
  if (voltage_sensor_) {
    float value = convert_raw_to_value(BL0906_V_RMS, data.voltage_raw);
    voltage_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "电压传感器: 原始值=%u, 计算值=%.2f", data.voltage_raw, value);
  }

  // 发布通道传感器数据
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (current_sensors_[i]) {
              float value = convert_raw_to_value(I_RMS_ADDR[i], data.channels[i].current_raw);
      current_sensors_[i]->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "通道%d电流: 原始值=%u, 计算值=%.2f", i+1, data.channels[i].current_raw, value);
    }
    
    if (power_sensors_[i]) {
              float value = convert_raw_to_value(WATT_ADDR[i], data.channels[i].power_raw);
      power_sensors_[i]->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "通道%d功率: 原始值=%d, 计算值=%.2f", i+1, data.channels[i].power_raw, value);
    }
    
    if (energy_sensors_[i]) {
      // 使用持久化CF_count而不是原始硬件值
      if (energy_persistence_enabled_) {
        float value = persistent_cf_count_[i] / Ke;  // 直接转换持久化CF_count
        energy_sensors_[i]->publish_state(value);
        ESP_LOGV(FACTORY_TAG, "通道%d电量: 持久化CF_count=%u, 计算值=%.6f kWh", i+1, persistent_cf_count_[i], value);
      } else {
        // 持久化禁用时使用原始硬件值
        float value = convert_raw_to_value(CF_CNT_ADDR[i], data.channels[i].energy_raw);
        energy_sensors_[i]->publish_state(value);
        ESP_LOGV(FACTORY_TAG, "通道%d电量: 原始值=%u, 计算值=%.2f", i+1, data.channels[i].energy_raw, value);
      }
    }
  }

  // 发布总和传感器数据
  if (power_sum_sensor_) {
    float value = convert_raw_to_value(BL0906_WATT_SUM, data.power_sum_raw);
    power_sum_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "总功率: 原始值=%d, 计算值=%.2f", data.power_sum_raw, value);
  }
  
  if (energy_sum_sensor_) {
    // 使用持久化CF_count而不是原始硬件值
    if (energy_persistence_enabled_) {
      float value = persistent_cf_count_[6] / Ke_sum;  // 使用持久化总和CF_count
      energy_sum_sensor_->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "总电量: 持久化CF_count=%u, 计算值=%.6f kWh", persistent_cf_count_[6], value);
    } else {
      // 持久化禁用时使用原始硬件值
      float value = convert_raw_to_value(BL0906_CF_SUM_CNT, data.energy_sum_raw);
      energy_sum_sensor_->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "总电量: 原始值=%u, 计算值=%.2f", data.energy_sum_raw, value);
    }
  }
}

// 辅助方法：检查通道是否有效（使用条件编译的常量）
bool BL0906Factory::is_valid_channel(int channel) const {
  return channel >= 0 && channel < MAX_CHANNELS;
}





// 简化的update方法
void BL0906Factory::update() {
  // 重置状态机到IDLE状态，开始新的数据读取周期
  this->current_state_ = State::IDLE;

  // 检查统计周期变化（不更新传感器）
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    energy_stats_manager_->check_period_changes();
  }

  // 定期更新所有 total_energy 传感器
  if (energy_persistence_enabled_) {
    // 更新各通道的累计电量传感器
    for (int i = 0; i < MAX_CHANNELS; i++) {
      if (total_energy_sensors_[i] != nullptr) {
        float total_energy = calculate_total_energy_from_cf_count(i);
        total_energy_sensors_[i]->publish_state(total_energy);
        ESP_LOGV(FACTORY_TAG, "定期更新通道%d累计电量: %.6f kWh", i+1, total_energy);
      }
    }
    
    // 更新总累计电量传感器
    if (total_energy_sum_sensor_ != nullptr) {
      float total_energy_sum = calculate_total_energy_from_cf_count(MAX_CHANNELS);
      total_energy_sum_sensor_->publish_state(total_energy_sum);
      ESP_LOGV(FACTORY_TAG, "定期更新总累计电量: %.6f kWh", total_energy_sum);
    }
  }
}

size_t BL0906Factory::enqueue_action_(ActionCallbackFuncPtr function) {
  this->action_queue_.push_back(function);
  return this->action_queue_.size();
}

void BL0906Factory::handle_actions_() {
  if (this->action_queue_.empty()) {
    return;
  }
  ActionCallbackFuncPtr ptr_func = nullptr;
  for (int i = 0; i < this->action_queue_.size(); i++) {
    ptr_func = this->action_queue_[i];
    if (ptr_func) {
      ESP_LOGI(FACTORY_TAG, "HandleActionCallback[%d]...", i);
      (this->*ptr_func)();
    }
  }

  this->action_queue_.clear();
}

// 现代化校准寄存器读取方法
void BL0906Factory::read_calib_register(CalibNumberType type, int channel) {
  uint8_t reg_addr = get_register_address(static_cast<CalibRegType>(type), channel);
  if (reg_addr == 0) {
    ESP_LOGW(FACTORY_TAG, "无效的校准寄存器类型或通道: type=%d, channel=%d", 
             static_cast<int>(type), channel);
    return;
  }

  ESP_LOGD(FACTORY_TAG, "读取校准寄存器: type=%d, channel=%d, 地址=0x%02X", 
           static_cast<int>(type), channel, reg_addr);
  
  int32_t value = read_register_value(reg_addr);
  ESP_LOGI(FACTORY_TAG, "校准寄存器 0x%02X 读取完成: %d", reg_addr, value);

  // 更新对应的Number组件
  number::Number* num = get_calib_number(type, channel);
  if (num) {
    num->publish_state(value);
    ESP_LOGD(FACTORY_TAG, "已更新Number组件状态: 0x%02X -> %d", reg_addr, value);
  } else {
    ESP_LOGW(FACTORY_TAG, "未找到对应的Number组件: type=%d, channel=%d", 
             static_cast<int>(type), channel);
  }
}



#ifndef USE_BL0906_FACTORY_SPI
// 旧的wait_until_available方法已被适配器替代

// 实现BL0906Factory类的refresh_all_calib_numbers方法
void BL0906Factory::refresh_all_calib_numbers() {
  ESP_LOGI(FACTORY_TAG, "刷新所有校准数字组件...");
  ESP_LOGI(FACTORY_TAG, "已注册的校准数字组件数量: %d", calib_numbers_.size());

  for (size_t i = 0; i < calib_numbers_.size(); i++) {
    auto *number = calib_numbers_[i];
    if (number != nullptr) {
      ESP_LOGD(FACTORY_TAG, "刷新第 %d 个校准数字组件: %p (寄存器: 0x%02X)",
               i, number, number->get_register_address());

      // 检查父指针是否正确设置
      if (!number->has_parent()) {
        ESP_LOGE(FACTORY_TAG, "第 %d 个校准数字组件的父指针为空！", i);
        continue;
      }

      number->update_from_register();
      // 防止看门狗复位
      App.feed_wdt();
    } else {
      ESP_LOGW(FACTORY_TAG, "第 %d 个校准数字组件指针为空", i);
    }
  }

  ESP_LOGI(FACTORY_TAG, "所有校准数字组件刷新完成");
}

// 旧的flush_rx_buffer方法已被适配器替代



// 修改read_register_value方法使用适配器
int32_t BL0906Factory::read_register_value(uint8_t address) {
  ESP_LOGI(FACTORY_TAG, "读取寄存器0x%02X的值", address);

  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return 0;
  }

  // 使用适配器读取数据
  // 适配器已经根据寄存器类型正确处理了数据格式（16位/24位，有符号/无符号）
  // 这里直接返回适配器处理后的结果，避免重复处理
  bool read_success = false;
  int32_t result = comm_adapter_->read_register(address, &read_success);
  
  if (!read_success) {
    ESP_LOGE(FACTORY_TAG, "读取寄存器0x%02X失败", address);
    return 0;  // 读取失败时返回0
  }

  ESP_LOGV(FACTORY_TAG, "寄存器0x%02X读取成功: %d", address, result);
  return result;
}

// 修改write_register_value方法使用适配器
bool BL0906Factory::write_register_value(uint8_t reg, int16_t value) {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return false;
  }
  
  // 在写入前解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，写入寄存器失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护已解除，继续写入寄存器 0x%02X", reg);
  
  return comm_adapter_->write_register(reg, value);
}

// 修改turn_off_write_protect方法，使用适配器
bool BL0906Factory::turn_off_write_protect() {
  ESP_LOGI(FACTORY_TAG, "开始执行关闭写保护操作");

  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return false;
  }

  // 写保护状态寄存器地址
  const uint8_t WR_PROTECT_REG = 0x9E;

  // 检查当前写保护状态
  bool read_success = false;
  int32_t raw_value = comm_adapter_->read_register(WR_PROTECT_REG, &read_success);
  if (!read_success) {
    ESP_LOGE(FACTORY_TAG, "读取写保护状态失败");
    return false;
  }

  // 分析写保护状态
  uint8_t low_byte = raw_value & 0xFF;
  uint8_t mid_byte = (raw_value >> 8) & 0xFF;
  uint16_t reg_16bit = (mid_byte << 8) | low_byte;  // 组合成16位值
  bool is_unlocked = (reg_16bit == 0x5555);  // 修正：检查16位值是否为0x5555
  
  ESP_LOGI(FACTORY_TAG, "写保护状态寄存器值: 0x%06X, 16位值: 0x%04X, 状态: %s", 
           raw_value & 0xFFFFFF, reg_16bit, is_unlocked ? "已解除" : "启用中");

  if (is_unlocked) {
    ESP_LOGI(FACTORY_TAG, "写保护已经是关闭状态，无需操作");
    return true;
  }

  ESP_LOGI(FACTORY_TAG, "开始解除寄存器写保护");
  bool success = false;

  // 重复尝试解锁，最多3次
  for (int attempt = 0; attempt < 3; attempt++) {
    // 发送写保护解锁命令 - 通过适配器发送原始命令
    ESP_LOGI(FACTORY_TAG, "发送写保护解锁命令 (尝试 %d)", attempt + 1);  // 改为LOGI确保能看到
    
    // 添加适配器状态检查
    if (!comm_adapter_) {
      ESP_LOGE(FACTORY_TAG, "通信适配器为空！");
      break;
    }
    
    std::string adapter_type = comm_adapter_->get_adapter_type();
    ESP_LOGI(FACTORY_TAG, "适配器类型: '%s'", adapter_type.c_str());
    
    // 根据适配器类型发送对应的解锁命令
    bool cmd_success = false;
    if (adapter_type == "UART") {
      // UART写保护解除命令
      uint8_t uart_unlock_cmd[6] = {0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7};
      ESP_LOGI(FACTORY_TAG, "发送UART写保护解锁命令");
      cmd_success = comm_adapter_->send_raw_command(uart_unlock_cmd, 6);
    } else if (adapter_type == "SPI") {
      // SPI写保护解除命令 - 向0x9E寄存器写入0x5555（16位）
      uint8_t spi_unlock_cmd[6] = {0x81, 0x9E, 0x00, 0x55, 0x55, 0x00};
      // 计算正确的校验和
      uint8_t checksum = (spi_unlock_cmd[0] + spi_unlock_cmd[1] + spi_unlock_cmd[2] + 
                         spi_unlock_cmd[3] + spi_unlock_cmd[4]) ^ 0xFF;
      spi_unlock_cmd[5] = checksum;
      
      ESP_LOGI(FACTORY_TAG, "发送SPI写保护解锁命令（写入0x5555到0x9E）: %02X %02X %02X %02X %02X %02X (校验和: %02X)", 
               spi_unlock_cmd[0], spi_unlock_cmd[1], spi_unlock_cmd[2], 
               spi_unlock_cmd[3], spi_unlock_cmd[4], spi_unlock_cmd[5], checksum);
      cmd_success = comm_adapter_->send_raw_command(spi_unlock_cmd, 6);
      ESP_LOGI(FACTORY_TAG, "SPI写保护解锁命令发送结果: %s", cmd_success ? "成功" : "失败");
    } else {
      ESP_LOGE(FACTORY_TAG, "未知的适配器类型: '%s'", adapter_type.c_str());
      break;
    }
    
    ESP_LOGI(FACTORY_TAG, "写保护解锁命令执行结果: %s", cmd_success ? "成功" : "失败");
    
    if (!cmd_success) {
      ESP_LOGW(FACTORY_TAG, "发送写保护解锁命令失败 (尝试 %d)", attempt + 1);
      continue;
    }
    
    // 延时以确保命令被处理
    delay(10);

    // 验证写保护是否成功解除
    read_success = false;
    raw_value = comm_adapter_->read_register(WR_PROTECT_REG, &read_success);
    if (!read_success) {
      ESP_LOGE(FACTORY_TAG, "读取写保护状态失败 (尝试 %d)", attempt + 1);
      continue;
    }

    low_byte = raw_value & 0xFF;
    mid_byte = (raw_value >> 8) & 0xFF;
    reg_16bit = (mid_byte << 8) | low_byte;  // 重新组合16位值
    is_unlocked = (reg_16bit == 0x5555);  // 修正：检查16位值是否为0x5555
    
    ESP_LOGD(FACTORY_TAG, "尝试 %d 后写保护状态: 0x%06X, 16位值: 0x%04X, 状态: %s", 
             attempt + 1, raw_value & 0xFFFFFF, reg_16bit, is_unlocked ? "已解除" : "启用中");
    
    if (is_unlocked) {
      ESP_LOGI(FACTORY_TAG, "写保护已成功解除 (尝试 %d)", attempt + 1);
      success = true;
      break;
    }

    ESP_LOGW(FACTORY_TAG, "写保护解锁尝试 %d 失败，重试...", attempt + 1);
    delay(50); // 在重试前等待更长时间
  }

  if (!success) {
    ESP_LOGE(FACTORY_TAG, "关闭写保护操作失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护关闭操作成功确认");
  return true;
}

// 寄存器类型判断函数已移至 bl0906_registers.h 作为内联函数，避免重复实现

// 修改setup方法，添加统计功能初始化
void BL0906Factory::setup() {
  ESP_LOGD(FACTORY_TAG, "开始初始化BL0906...");

  // 初始化互斥锁
  this->init_mutex();

  // 初始化通信适配器
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置！请检查配置");
    this->mark_failed();
    return;
  }

  if (!comm_adapter_->initialize()) {
    ESP_LOGE(FACTORY_TAG, "通信适配器初始化失败: %s", comm_adapter_->get_last_error().c_str());
    this->mark_failed();
    return;
  }

  ESP_LOGI(FACTORY_TAG, "通信适配器初始化成功: %s", comm_adapter_->get_adapter_type().c_str());

  // 执行适配器自检
  if (!comm_adapter_->self_test()) {
    ESP_LOGW(FACTORY_TAG, "通信适配器自检失败: %s", comm_adapter_->get_last_error().c_str());
  } else {
    ESP_LOGI(FACTORY_TAG, "通信适配器自检通过");
  }

  // 电网频率自动适配
  if (freq_adapt_mode_ != FreqAdaptMode::OFF && !freq_adapted_) {
    switch (freq_adapt_mode_) {
      case FreqAdaptMode::AUTO:
        ESP_LOGI(FACTORY_TAG, "开始电网频率自动检测...");
        {
          float detected_freq = detect_grid_frequency();
          if (detected_freq > 0) {
            bool is_60hz = (detected_freq >= FREQ_DETECT_THRESHOLD_LOW && 
                           detected_freq <= FREQ_DETECT_THRESHOLD_HIGH);
            
            ESP_LOGI(FACTORY_TAG, "检测到电网频率: %.2fHz，设置为%s模式", 
                    detected_freq, is_60hz ? "60Hz" : "50Hz");
                    
            if (set_ac_frequency_mode(is_60hz)) {
              freq_adapted_ = true;
              ESP_LOGI(FACTORY_TAG, "电网频率自动适配完成");
            } else {
              ESP_LOGW(FACTORY_TAG, "电网频率适配失败，使用默认50Hz模式");
            }
          } else {
            ESP_LOGW(FACTORY_TAG, "频率检测失败，使用默认50Hz模式");
          }
        }
        break;
        
      case FreqAdaptMode::HZ60:
        ESP_LOGI(FACTORY_TAG, "强制设置为60Hz模式...");
        if (set_ac_frequency_mode(true)) {
          freq_adapted_ = true;
          ESP_LOGI(FACTORY_TAG, "强制60Hz模式设置完成");
        } else {
          ESP_LOGW(FACTORY_TAG, "60Hz模式设置失败，使用默认50Hz模式");
        }
        break;
        
      default:
        break;
    }
  }

  // 检查实例ID是否已设置（现在是必填项）
  if (instance_id_ == 0) {
      ESP_LOGE(FACTORY_TAG, "错误：instance_id未设置！请在YAML配置中设置instance_id");
      return;
  }
  ESP_LOGI(FACTORY_TAG, "使用配置的实例ID: 0x%08X", instance_id_);

  // 初始化新的统一校准存储系统
  if (!init_calibration_storage()) {
      ESP_LOGE(FACTORY_TAG, "校准存储初始化失败");
      this->mark_failed();
      return;
  }
  
  // 加载校准数据
  bool calibration_loaded = load_calibration_data();
  if (!calibration_loaded) {
      ESP_LOGW(FACTORY_TAG, "校准数据加载失败，使用默认值");
      #ifdef BL0906_CALIBRATION_MODE
          // 校准版：尝试使用YAML配置
          apply_calibration_values();
      #else
          // 量产版：如果有YAML配置的校准值，也应该应用
          if (!initial_calibration_values_.empty()) {
              ESP_LOGI(FACTORY_TAG, "量产版本检测到YAML配置的校准值，开始应用");
              apply_calibration_values();
          } else {
              ESP_LOGW(FACTORY_TAG, "量产版本没有校准数据，芯片将使用默认校准值运行");
          }
      #endif
  } else {
      ESP_LOGI(FACTORY_TAG, "成功从存储加载校准数据并应用到芯片");
  }

  // 设置电量持久化存储
  setup_energy_persistence();

  // 初始化电量统计管理器
  if (energy_statistics_enabled_) {
    energy_stats_manager_.reset(new EnergyStatisticsManager(this));
    // EnergyStatisticsManager继承自PollingComponent，会被ESPHome框架自动管理
    // 不需要手动注册为子组件
    energy_stats_manager_->setup();
    ESP_LOGI(FACTORY_TAG, "电量统计管理器初始化完成，更新间隔: 60秒");
  }

  // 测试保存功能是否正常工作
  if (energy_persistence_enabled_) {
    ESP_LOGI(FACTORY_TAG, "测试电量数据保存功能...");
    save_energy_data();  // 立即保存一次，测试功能是否正常
  }

  // 读取校准寄存器
  refresh_all_calib_numbers();
  ESP_LOGI(FACTORY_TAG, "BL0906初始化完成，已注册 %d 个校准数字组件", calib_numbers_.size());
}

// 新增方法：应用校准值
void BL0906Factory::apply_calibration_values() {
  const size_t total_values = initial_calibration_values_.size();

  if (total_values == 0) {
    ESP_LOGI(FACTORY_TAG, "没有校准值需要应用");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "开始应用 %zu 个校准值", total_values);

  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，校准值未应用");
    return;
  }

  // 批量写入所有校准值
  int success_count = 0;
  size_t current = 0;

  for (const auto& pair : initial_calibration_values_) {
    uint8_t reg_addr = pair.first;
    int16_t value = pair.second;
    current++;

    // 每5个值或最后一个值显示进度
    if (current % 5 == 0 || current == total_values) {
      ESP_LOGI(FACTORY_TAG, "应用进度: %zu/%zu (%.1f%%)",
               current, total_values, current * 100.0f / total_values);
    }

    ESP_LOGD(FACTORY_TAG, "写入寄存器 0x%02X 值: %d", reg_addr, value);

    bool write_success = false;
    
    // 统一使用write_register_value方法，适配器已正确处理所有寄存器类型的符号扩展
    write_success = write_register_value(reg_addr, value);
    ESP_LOGD(FACTORY_TAG, "写入校准寄存器 0x%02X: %d", reg_addr, value);

    if (write_success) {
      success_count++;

            // 防止看门狗复位
      App.feed_wdt();

      // 短暂延时，避免连续写入过快
      delay(2);
    } else {
      // 写入失败时也要喂狗
      App.feed_wdt();
    }
  }

  ESP_LOGI(FACTORY_TAG, "校准值应用完成：%d 成功，%d 失败",
          success_count, (int)(total_values - success_count));
}

// 实现register_calib_number方法
void BL0906Factory::register_calib_number(BL0906Number *number) {
  number->set_parent(this);  // 设置父指针
  calib_numbers_.push_back(number);
}

// 电量持久化存储相关方法实现

// 设置电量持久化存储（基于CF_count）
void BL0906Factory::setup_energy_persistence() {
  ESP_LOGI(FACTORY_TAG, "初始化电量持久化存储系统（基于CF_count）...");
  ESP_LOGI(FACTORY_TAG, "电量持久化状态: %s", energy_persistence_enabled_ ? "启用" : "禁用");

  if (!energy_persistence_enabled_) {
    ESP_LOGW(FACTORY_TAG, "⚠️ 电量持久化存储已禁用，断电后CF_count数据将丢失");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "创建Preferences对象...");
  ESP_LOGI(FACTORY_TAG, "数据结构大小: %d 字节", sizeof(EnergyPersistenceData));
  ESP_LOGI(FACTORY_TAG, "Hash值: 0x%08X", 0x906CF0FF); // 新的hash值，避免与旧版本冲突

  // 创建preferences对象（使用新的数据结构）
  energy_pref_ = global_preferences->make_preference<EnergyPersistenceData>(
    sizeof(EnergyPersistenceData), 0x906CF0FF); // "bl0906_cf_count" hash

  ESP_LOGI(FACTORY_TAG, "✅ Preferences对象创建成功");

  // 加载已保存的CF_count数据
  load_energy_data();

  ESP_LOGI(FACTORY_TAG, "✅ 电量持久化存储初始化完成");
}

// 加载CF_count数据（修正版本）
void BL0906Factory::load_energy_data() {
  if (!energy_persistence_enabled_) {
    ESP_LOGW(FACTORY_TAG, "电量持久化存储已禁用，无法加载数据");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "开始加载CF_count持久化数据...");
  ESP_LOGI(FACTORY_TAG, "数组大小常量 ARRAY_SIZE: %d", ARRAY_SIZE);

  EnergyPersistenceData data;
  // 先清零数据结构，避免未初始化的内存
  memset(&data, 0, sizeof(data));
  
  bool load_success = energy_pref_.load(&data);
  
  ESP_LOGI(FACTORY_TAG, "Preferences加载结果: %s", load_success ? "成功" : "失败");
  
  if (load_success) {
    // 修复：使用ARRAY_SIZE常量替代硬编码的7
    uint32_t calculated_checksum = 0;
    
    // 分别计算每个数组的校验和，避免溢出
    for (int i = 0; i < ARRAY_SIZE; i++) {
      calculated_checksum += data.persistent_cf_count[i];
      ESP_LOGV(FACTORY_TAG, "persistent_cf_count[%d]: %u", i, data.persistent_cf_count[i]);
    }
    
    for (int i = 0; i < ARRAY_SIZE; i++) {
      calculated_checksum += data.last_cf_count[i];
      ESP_LOGV(FACTORY_TAG, "last_cf_count[%d]: %u", i, data.last_cf_count[i]);
    }
    
    // 将save_count也加入校验和计算
    calculated_checksum += data.save_count;
    
    ESP_LOGI(FACTORY_TAG, "数据校验: 计算值=0x%08X, 存储值=0x%08X", 
             calculated_checksum, data.checksum);
    ESP_LOGI(FACTORY_TAG, "存储的总持久化CF_count: %u, 存储计数: %u", 
             data.persistent_cf_count[ARRAY_SIZE-1], data.save_count);
    
    // 数据有效性预检查：检查是否有异常大值
    bool data_seems_valid = true;
    for (int i = 0; i < ARRAY_SIZE; i++) {
      if (data.persistent_cf_count[i] > 0x7FFFFFFF || data.last_cf_count[i] > 0x7FFFFFFF) {
        ESP_LOGW(FACTORY_TAG, "检测到可疑的大值: persistent[%d]=%u, last[%d]=%u", 
                 i, data.persistent_cf_count[i], i, data.last_cf_count[i]);
        data_seems_valid = false;
      }
    }
    
    if (calculated_checksum == data.checksum && data_seems_valid) {
      // 数据有效，恢复CF_count数据和存储计数
      ESP_LOGI(FACTORY_TAG, "数据校验通过，开始恢复数据...");
      
      for (int i = 0; i < ARRAY_SIZE; i++) {
        persistent_cf_count_[i] = data.persistent_cf_count[i];
        last_cf_count_[i] = data.last_cf_count[i];
        
        if (i < CHANNEL_COUNT) {
          ESP_LOGI(FACTORY_TAG, "恢复通道%d: 持久化CF_count=%u, 上次硬件CF_count=%u", 
                   i+1, persistent_cf_count_[i], last_cf_count_[i]);
        } else {
          ESP_LOGI(FACTORY_TAG, "恢复总和: 持久化CF_count=%u, 上次硬件CF_count=%u", 
                   persistent_cf_count_[i], last_cf_count_[i]);
        }
      }

      // 恢复存储计数
      current_save_count_ = data.save_count;
      
      // 加载数据成功后，初始化 saved_total_cf_ - 使用CHANNEL_COUNT而非硬编码
      saved_total_cf_ = 0;
      for (int i = 0; i < CHANNEL_COUNT; i++) {
        saved_total_cf_ += persistent_cf_count_[i];
      }
      ESP_LOGI(FACTORY_TAG, "初始化saved_total_cf: %u", saved_total_cf_);

      ESP_LOGI(FACTORY_TAG, "✅ 成功加载CF_count数据，存储计数: %u", current_save_count_);
    } else {
      if (!data_seems_valid) {
        ESP_LOGE(FACTORY_TAG, "❌ CF_count数据包含异常值，可能已损坏！");
      } else {
        ESP_LOGE(FACTORY_TAG, "❌ CF_count数据校验失败！计算值=0x%08X, 存储值=0x%08X", 
                 calculated_checksum, data.checksum);
      }
      ESP_LOGW(FACTORY_TAG, "数据已损坏，重置为0并重新开始计算");
      reset_energy_data();
    }
  } else {
    ESP_LOGW(FACTORY_TAG, "⚠️ 未找到已保存的CF_count数据，这可能是首次启动");
    ESP_LOGI(FACTORY_TAG, "初始化CF_count数据，从0开始计算");
    reset_energy_data();
  }
}

// 简化的电量数据保存方法
void BL0906Factory::save_energy_data() {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过保存");
    return;
  }

  current_save_count_++;
  ESP_LOGI(FACTORY_TAG, "执行CF_count数据保存，存储计数: %u", current_save_count_);

  // 准备保存数据结构
  EnergyPersistenceData data;
  // 先清零数据结构，确保没有垃圾数据
  memset(&data, 0, sizeof(data));
  
  // 复制所有数据
  memcpy(data.persistent_cf_count, persistent_cf_count_, sizeof(persistent_cf_count_));
  memcpy(data.last_cf_count, last_cf_count_, sizeof(last_cf_count_));
  data.save_count = current_save_count_;
  
  // 修复：使用ARRAY_SIZE常量计算校验和
  data.checksum = 0;
  for (int i = 0; i < ARRAY_SIZE; i++) {
    data.checksum += data.persistent_cf_count[i];
    data.checksum += data.last_cf_count[i];
  }
  data.checksum += data.save_count;

  // 保存前验证数据合理性
  bool data_reasonable = true;
  for (int i = 0; i < ARRAY_SIZE; i++) {
    if (data.persistent_cf_count[i] > 0x7FFFFFFF || data.last_cf_count[i] > 0x7FFFFFFF) {
      ESP_LOGW(FACTORY_TAG, "准备保存的数据中检测到异常大值: persistent[%d]=%u, last[%d]=%u", 
               i, data.persistent_cf_count[i], i, data.last_cf_count[i]);
      data_reasonable = false;
    }
  }
  
  if (!data_reasonable) {
    ESP_LOGE(FACTORY_TAG, "❌ 数据包含异常值，取消保存操作");
    current_save_count_--;  // 回退存储计数器
    return;
  }

  // 保存到flash
  bool save_success = energy_pref_.save(&data);
  
  if (save_success) {
    ESP_LOGI(FACTORY_TAG, "✅ CF_count数据保存成功，总持久化CF_count: %u, 存储计数: %u", 
             persistent_cf_count_[ARRAY_SIZE-1], current_save_count_);
  } else {
    ESP_LOGE(FACTORY_TAG, "❌ 保存CF_count数据失败！Flash可能已满或损坏");
    current_save_count_--;  // 保存失败时回退存储计数器
  }

  last_save_time_ = millis();
}

// 重置CF_count数据（修正版本）
void BL0906Factory::reset_energy_data() {
  for (int i = 0; i < ARRAY_SIZE; i++) {
    persistent_cf_count_[i] = 0;
    last_cf_count_[i] = 0;
  }
  
  // 重置saved_total_cf_
  saved_total_cf_ = 0;

  ESP_LOGI(FACTORY_TAG, "CF_count数据已重置，脉冲计数将重新初始化，存储计数已重置为0");

  // 立即保存重置后的数据
  save_energy_data();
}

// 新增：检测并修复损坏的持久化数据
void BL0906Factory::detect_and_repair_corrupted_data() {
  ESP_LOGI(FACTORY_TAG, "开始检测持久化数据完整性...");
  
  bool data_corrupted = false;
  
  // 检查内存中的数据
  for (int i = 0; i < ARRAY_SIZE; i++) {
    if (persistent_cf_count_[i] > 0x7FFFFFFF) {
      ESP_LOGW(FACTORY_TAG, "检测到异常的persistent_cf_count[%d]: %u", i, persistent_cf_count_[i]);
      data_corrupted = true;
    }
    if (last_cf_count_[i] > 0x7FFFFFFF) {
      ESP_LOGW(FACTORY_TAG, "检测到异常的last_cf_count[%d]: %u", i, last_cf_count_[i]);
      data_corrupted = true;
    }
  }
  
  if (data_corrupted) {
    ESP_LOGW(FACTORY_TAG, "⚠️ 检测到数据损坏，执行修复操作...");
    
    // 修复策略1：重置异常值为0
    for (int i = 0; i < ARRAY_SIZE; i++) {
      if (persistent_cf_count_[i] > 0x7FFFFFFF) {
        ESP_LOGI(FACTORY_TAG, "重置persistent_cf_count[%d] 从 %u 到 0", i, persistent_cf_count_[i]);
        persistent_cf_count_[i] = 0;
      }
      if (last_cf_count_[i] > 0x7FFFFFFF) {
        ESP_LOGI(FACTORY_TAG, "重置last_cf_count[%d] 从 %u 到 0", i, last_cf_count_[i]);
        last_cf_count_[i] = 0;
      }
    }
    
    // 重新计算saved_total_cf_
    saved_total_cf_ = 0;
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      saved_total_cf_ += persistent_cf_count_[i];
    }
    
    // 保存修复后的数据
    save_energy_data();
    
    ESP_LOGI(FACTORY_TAG, "✅ 数据修复完成并已保存");
  } else {
    ESP_LOGI(FACTORY_TAG, "✅ 数据完整性检查通过，无需修复");
  }
}


// 统计功能相关方法实现
void BL0906Factory::set_energy_statistics_enabled(bool enabled) {
  energy_statistics_enabled_ = enabled;
  ESP_LOGI(FACTORY_TAG, "电量统计功能%s", enabled ? "已启用" : "已禁用");
}

void BL0906Factory::set_time_component(esphome_time::RealTimeClock *time_comp) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_time_component(time_comp);
  }
}

void BL0906Factory::set_statistics_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_sensor(type, sensor, channel);
  }
}

void BL0906Factory::set_statistics_update_interval(uint32_t update_interval_ms) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_update_interval(update_interval_ms);
    ESP_LOGI(FACTORY_TAG, "设置统计传感器更新间隔: %u毫秒", update_interval_ms);
  }
}

float BL0906Factory::calculate_total_energy_from_cf_count(int channel) const {
  if (channel < 0 || channel >= ARRAY_SIZE) {
    return 0.0f;
  }
  return persistent_cf_count_[channel] / Ke;  // 将CF_count转换为电量(kWh)
}

// 诊断方法实现
void BL0906Factory::diagnose_energy_persistence() {
  ESP_LOGI(FACTORY_TAG, "=== 电量持久化诊断信息（基于CF_count）===");
  ESP_LOGI(FACTORY_TAG, "持久化状态: %s", energy_persistence_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "统计功能状态: %s", energy_statistics_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "芯片重启检测状态: %s", chip_restart_detected_ ? "已检测到重启" : "正常运行");
  if (chip_restart_detected_) {
    ESP_LOGI(FACTORY_TAG, "上次重启检测时间: %u ms", last_restart_detection_time_);
    ESP_LOGI(FACTORY_TAG, "距离上次重启检测: %u ms", millis() - last_restart_detection_time_);
  }
  ESP_LOGI(FACTORY_TAG, "芯片重启次数: %u", chip_restart_count_);
  ESP_LOGI(FACTORY_TAG, "上次保存时间: %u ms", last_save_time_);
  ESP_LOGI(FACTORY_TAG, "距离上次保存: %u ms", millis() - last_save_time_);
  ESP_LOGI(FACTORY_TAG, "存储计数: %u 次", current_save_count_);
  ESP_LOGI(FACTORY_TAG, "数据结构大小: %d 字节", sizeof(EnergyPersistenceData));
  
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    float channel_energy = calculate_total_energy_from_cf_count(i);
    // 内联get_persistent_cf_count的代码
    uint32_t persistent_cf_count = (i >= 0 && i < CHANNEL_COUNT) ? persistent_cf_count_[i] : 0;
    ESP_LOGI(FACTORY_TAG, "通道%d: 持久化CF_count=%u, last_cf_count=%u, 电量=%.6f kWh, 硬件CF_count=%u", 
             i+1, persistent_cf_count, last_cf_count_[i],channel_energy, current_data_.channels[i].energy_raw);
  }
  
  
  float total_energy = calculate_total_energy_from_cf_count(6);
  // 内联get_persistent_cf_count_sum的代码
  uint32_t persistent_cf_count_sum = persistent_cf_count_[6];
  ESP_LOGI(FACTORY_TAG, "总和: 持久化CF_count=%u, last_cf_count=%u,电量=%.6f kWh, 硬件CF_count=%u", 
           persistent_cf_count_sum, last_cf_count_[6],total_energy, current_data_.energy_sum_raw);
  ESP_LOGI(FACTORY_TAG, "========================================");
}

// 设置电量持久化开关
void BL0906Factory::set_energy_persistence_enabled(bool enabled) {
  energy_persistence_enabled_ = enabled;
  ESP_LOGI(FACTORY_TAG, "电量持久化存储%s", enabled ? "已启用" : "已禁用");
}

// 新增方法：检查并同步硬件CF_count（如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖）
void BL0906Factory::check_and_sync_hardware_cf_count() {
  ESP_LOGI(FACTORY_TAG, "开始检查并同步硬件CF_count...");

  bool data_changed = false;  // 标记是否有数据变化

  // 通信适配器会自动处理缓冲区管理

  // 读取各通道的硬件CF_count
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    bool read_success = false;
    int32_t raw_value = send_read_command_and_receive(CF_CNT_ADDR[i], &read_success);
    if (!read_success) {
      ESP_LOGE(FACTORY_TAG, "读取通道%d硬件CF_count失败", i+1);
      continue;
    }

    uint32_t hardware_cf_count = static_cast<uint32_t>(raw_value);
    ESP_LOGD(FACTORY_TAG, "通道%d硬件CF_count: %u, 持久化CF_count: %u", 
             i+1, hardware_cf_count, persistent_cf_count_[i]);

    // 如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖
    if (persistent_cf_count_[i] < hardware_cf_count) {
      ESP_LOGI(FACTORY_TAG, "通道%d: 持久化CF_count=%u < 硬件CF_count=%u，用硬件CF_count覆盖",
               i+1, persistent_cf_count_[i], hardware_cf_count);
      persistent_cf_count_[i] = hardware_cf_count;
      last_cf_count_[i] = hardware_cf_count;  // 同时更新基准值
      data_changed = true;
    } else {
      ESP_LOGD(FACTORY_TAG, "通道%d: 持久化CF_count=%u >= 硬件CF_count=%u，保持持久化数据",
               i+1, persistent_cf_count_[i], hardware_cf_count);
    }

    // 短暂延时，避免连续读取过快
    delay(5);
  }

  // 读取总和的硬件CF_count
  bool sum_read_success = false;
  int32_t sum_raw_value = send_read_command_and_receive(CF_SUM_ADDR, &sum_read_success);
  if (sum_read_success) {
    uint32_t hardware_cf_sum = static_cast<uint32_t>(sum_raw_value);
    ESP_LOGD(FACTORY_TAG, "总和硬件CF_count: %u, 持久化CF_count: %u", 
             hardware_cf_sum, persistent_cf_count_[6]);

    // 如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖
    if (persistent_cf_count_[6] < hardware_cf_sum) {
      ESP_LOGI(FACTORY_TAG, "总和: 持久化CF_count=%u < 硬件CF_count=%u，用硬件CF_count覆盖",
               persistent_cf_count_[6], hardware_cf_sum);
      persistent_cf_count_[6] = hardware_cf_sum;
      last_cf_count_[6] = hardware_cf_sum;  // 同时更新基准值
      data_changed = true;
    } else {
      ESP_LOGD(FACTORY_TAG, "总和: 持久化CF_count=%u >= 硬件CF_count=%u，保持持久化数据",
               persistent_cf_count_[6], hardware_cf_sum);
    }
  } else {
    ESP_LOGE(FACTORY_TAG, "读取总和硬件CF_count失败");
  }

  // 如果有数据变化，立即保存
  if (data_changed) {
    ESP_LOGI(FACTORY_TAG, "检测到数据变化，立即保存持久化数据");
    save_energy_data();
  }

  ESP_LOGI(FACTORY_TAG, "硬件CF_count同步完成");
}

// RMSOS自动计算函数（合并简化版本）
void BL0906Factory::calculate_and_write_rmsos_all_channels() {
  ESP_LOGI(FACTORY_TAG, "开始RMSOS自动计算...");
  
  // 检查组件状态
  if (!this->is_ready()) {
    ESP_LOGE(FACTORY_TAG, "BL0906组件未就绪，无法执行RMSOS计算");
    return;
  }
  
  // 检查数据读取完整性
  if (!current_data_.read_complete) {
    ESP_LOGW(FACTORY_TAG, "数据读取未完成，使用当前可用数据进行计算");
  }
  
  // 2. 使用主循环已读取的电流原始值进行RMSOS计算
  bool success = true;
  ESP_LOGI(FACTORY_TAG, "使用主循环数据(时间戳: %u, 数据完整性: %s)", 
           current_data_.timestamp, current_data_.read_complete ? "完整" : "部分");
  
  for (int channel = 1; channel <= 6; channel++) {
    // 直接使用已读取的电流原始值（现在是uint32_t类型）
    uint32_t current_raw = current_data_.channels[channel - 1].current_raw;
    
    // 检查数据有效性
    if (current_raw == 0) {
      ESP_LOGW(FACTORY_TAG, "通道%d电流原始值为0，可能表示无负载或读取异常", channel);
    }
    
    // 内联计算RMSOS值：RMSOS[n] = (-pow(current_raw[n], 2) / 256) * 0.787
    double current_squared = pow(static_cast<double>(current_raw), 2);
    double result = (-current_squared / 256.0) * 0.787;
    
    // 将结果转换为16位有符号整数（统一使用write_register_value方法）
    int32_t rmsos_value_temp = static_cast<int32_t>(result);
    
    // 确保值在16位有符号范围内 (-32768 到 32767)，因为write_register_value使用int16_t
    if (rmsos_value_temp > 32767) {
      rmsos_value_temp = 32767;
    } else if (rmsos_value_temp < -32768) {
      rmsos_value_temp = -32768;
    }
    
    int16_t rmsos_value = static_cast<int16_t>(rmsos_value_temp);
    
    ESP_LOGD(FACTORY_TAG, "通道%d计算: current_raw=%u, result=%.2f, rmsos_value=%d", 
             channel, current_raw, result, rmsos_value);
    
    // 写入RMSOS寄存器（统一使用write_register_value方法）
    uint8_t address = RMSOS_ADDR[channel - 1]; // 通道1-6对应数组索引0-5
    if (!write_register_value(address, rmsos_value)) {
      ESP_LOGE(FACTORY_TAG, "写入通道%d RMSOS寄存器(0x%02X)失败", channel, address);
      success = false;
    } else {
      ESP_LOGI(FACTORY_TAG, "通道%d: 电流原始值=%u, RMSOS值=%d, 写入成功", 
               channel, current_raw, rmsos_value);
    }
    
    // 短暂延时避免连续写入过快
    delay(10);
  }
  
  // 3. 恢复写保护（如果原来是开启的）
  // 注意：这里可能需要根据实际情况决定是否重新开启写保护
  
  if (success) {
    ESP_LOGI(FACTORY_TAG, "RMSOS自动计算完成，所有通道处理成功");
    // 刷新所有校准数字组件显示
    refresh_all_calib_numbers();
  } else {
    ESP_LOGW(FACTORY_TAG, "RMSOS自动计算完成，但部分通道处理失败");
  }
}

// 从存储加载校准值（兼容旧接口）
void BL0906Factory::load_calibration_from_flash() {
    // 使用新的统一存储接口
    load_calibration_data();
}

#ifdef BL0906_CALIBRATION_MODE
// 批量保存所有校准值到存储（由Button组件手动调用）
void BL0906Factory::save_all_calibration_to_flash() {
    ESP_LOGI(FACTORY_TAG, "开始手动保存校准数据到存储...");
    
    // 使用新的统一存储接口
    if (save_calibration_data()) {
        ESP_LOGI(FACTORY_TAG, "校准数据手动保存完成");
    } else {
        ESP_LOGE(FACTORY_TAG, "手动保存校准数据失败");
    }
}
#endif

// 实例ID相关方法实现
void BL0906Factory::set_instance_id(uint32_t id) {
    instance_id_ = id;
    ESP_LOGI(FACTORY_TAG, "设置实例ID: 0x%08X", instance_id_);
    
    // 如果校准存储已初始化，更新其实例ID
    #ifdef USE_I2C_EEPROM_CALIBRATION
    if (calibration_storage_) {
        // 对于I2C EEPROM存储，可能需要设置实例ID
        // 这里暂时不做处理，因为preference存储不需要额外的实例ID设置
        ESP_LOGD(FACTORY_TAG, "校准存储已初始化，实例ID已更新");
    }
    #endif
}

uint32_t BL0906Factory::get_instance_id() const {
    return instance_id_;
}

uint32_t BL0906Factory::generate_instance_id() const {
    // 不再自动生成ID，instance_id现在是必填项
    // 如果没有设置instance_id，返回默认值并记录警告
    if (instance_id_ == 0) {
        ESP_LOGW(FACTORY_TAG, "instance_id未设置，使用默认值0x906B0001");
        return 0x906B0001;  // 默认ID
    }
    
    return instance_id_;
}

// 新的统一存储系统实现
bool BL0906Factory::init_calibration_storage() {
    if (storage_type_ == "preference") {
        ESP_LOGI(FACTORY_TAG, "初始化preference校准存储");
        calibration_storage_.reset(new PreferenceCalibrationStorage());
    } 
#ifdef USE_I2C_EEPROM_CALIBRATION
    else if (storage_type_ == "eeprom") {
        ESP_LOGI(FACTORY_TAG, "初始化I2C EEPROM校准存储 (型号: %02X, 地址: 0x%02X)", 
                 static_cast<uint8_t>(eeprom_type_), i2c_address_);
        calibration_storage_.reset(new I2CEEPROMCalibrationStorage(
            i2c_parent_, eeprom_type_, i2c_address_));
    }
#endif
    else {
        ESP_LOGE(FACTORY_TAG, "未知的存储类型: %s", storage_type_.c_str());
        return false;
    }
    
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "创建校准存储对象失败");
        return false;
    }
    
    if (!calibration_storage_->init()) {
        ESP_LOGE(FACTORY_TAG, "校准存储初始化失败");
        return false;
    }
    
    ESP_LOGI(FACTORY_TAG, "校准存储初始化成功 (类型: %s)", storage_type_.c_str());
    return true;
}

// 创建与当前配置匹配的校准存储实例
std::unique_ptr<CalibrationStorageInterface> BL0906Factory::create_storage_instance() {
    std::unique_ptr<CalibrationStorageInterface> storage;
    
    if (storage_type_ == "preference") {
        ESP_LOGD(FACTORY_TAG, "创建preference校准存储实例");
        storage.reset(new PreferenceCalibrationStorage());
    } 
#ifdef USE_I2C_EEPROM_CALIBRATION
    else if (storage_type_ == "eeprom") {
        ESP_LOGD(FACTORY_TAG, "创建I2C EEPROM校准存储实例 (型号: %02X, 地址: 0x%02X)", 
                 static_cast<uint8_t>(eeprom_type_), i2c_address_);
        storage.reset(new I2CEEPROMCalibrationStorage(
            i2c_parent_, eeprom_type_, i2c_address_));
    }
#endif
    else {
        ESP_LOGE(FACTORY_TAG, "未知的存储类型: %s", storage_type_.c_str());
        return nullptr;
    }
    
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建校准存储实例失败");
        return nullptr;
    }
    
    if (!storage->init()) {
        ESP_LOGE(FACTORY_TAG, "校准存储实例初始化失败");
        return nullptr;
    }
    
    return storage;
}

// 封装的校准数据操作方法实现
void BL0906Factory::read_and_display_calibration_data() {
    ESP_LOGI(FACTORY_TAG, "开始读取持久化存储的校准数据...");
    
    // 获取实例ID
    uint32_t instance_id = this->get_instance_id();
    ESP_LOGI(FACTORY_TAG, "当前实例ID: 0x%08X", instance_id);
    
    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建校准存储对象失败");
        return;
    }
    
    // 读取校准数据
    std::vector<CalibrationEntry> entries;
    if (storage->read_instance(instance_id, entries)) {
        ESP_LOGI(FACTORY_TAG, "成功读取 %d 个校准条目:", entries.size());
        
        // 显示所有校准数据
        for (size_t i = 0; i < entries.size(); i++) {
            const auto& entry = entries[i];
            ESP_LOGI(FACTORY_TAG, "  [%d] 寄存器: 0x%02X, 值: %d", 
                     i, entry.register_addr, entry.value);
        }
        
        // 更新Number组件显示
        ESP_LOGI(FACTORY_TAG, "正在更新Number组件显示...");
        this->refresh_all_calib_numbers();
        
    } else {
        ESP_LOGW(FACTORY_TAG, "实例 0x%08X 的校准数据不存在或读取失败", instance_id);
    }
}

void BL0906Factory::show_all_instances_calibration_data() {
    ESP_LOGI(FACTORY_TAG, "开始读取所有实例的校准数据...");
    
    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建校准存储对象失败");
        return;
    }
    
    // 获取所有实例列表
    std::vector<uint32_t> instance_list = storage->get_instance_list();
    ESP_LOGI(FACTORY_TAG, "找到 %d 个实例:", instance_list.size());
    
    // 遍历所有实例
    for (uint32_t instance_id : instance_list) {
        ESP_LOGI(FACTORY_TAG, "=== 实例 0x%08X ===", instance_id);
        
        std::vector<CalibrationEntry> entries;
        if (storage->read_instance(instance_id, entries)) {
            ESP_LOGI(FACTORY_TAG, "  校准条目数量: %d", entries.size());
            
            for (size_t i = 0; i < entries.size(); i++) {
                const auto& entry = entries[i];
                ESP_LOGI(FACTORY_TAG, "    [%d] 寄存器: 0x%02X, 值: %d", 
                         i, entry.register_addr, entry.value);
            }
        } else {
            ESP_LOGW(FACTORY_TAG, "  读取实例数据失败");
        }
    }
    
    if (instance_list.empty()) {
        ESP_LOGI(FACTORY_TAG, "没有找到任何校准数据实例");
    }
}

void BL0906Factory::show_storage_status() {
    ESP_LOGI(FACTORY_TAG, "查询存储状态...");
    
    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建存储对象失败");
        return;
    }
    
    // 获取实例列表
    auto instance_list = storage->get_instance_list();
    
    ESP_LOGI(FACTORY_TAG, "=== 存储状态信息 ===");
    auto storage_type = storage->get_storage_type();
    ESP_LOGI(FACTORY_TAG, "存储类型: %s", storage_type.c_str());
    ESP_LOGI(FACTORY_TAG, "当前实例数: %d", instance_list.size());
    
    // 对于EEPROM，显示详细信息
    if (storage->is_eeprom_storage()) {
        size_t max_instances = storage->get_max_instances();
        ESP_LOGI(FACTORY_TAG, "EEPROM型号: 24C02 (256字节)");
        ESP_LOGI(FACTORY_TAG, "最大实例数: %d", max_instances);
        ESP_LOGI(FACTORY_TAG, "剩余可用槽位: %d", max_instances - instance_list.size());
    }
    
    if (!instance_list.empty()) {
        ESP_LOGI(FACTORY_TAG, "已存储的实例:");
        for (size_t i = 0; i < instance_list.size(); i++) {
            ESP_LOGI(FACTORY_TAG, "  [%d] 实例ID: 0x%08X", i, instance_list[i]);
        }
    } else {
        ESP_LOGI(FACTORY_TAG, "存储为空，可以保存新实例");
    }
    
    uint32_t current_instance = this->get_instance_id();
    ESP_LOGI(FACTORY_TAG, "当前配置的实例ID: 0x%08X", current_instance);
}

void BL0906Factory::clear_calibration_storage() {
    ESP_LOGI(FACTORY_TAG, "开始清除存储数据...");

    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建存储对象失败");
        return;
    }

    // 清除存储
    if (storage->erase()) {
        ESP_LOGI(FACTORY_TAG, "存储清除成功！现在可以保存校准数据了");
    } else {
        ESP_LOGE(FACTORY_TAG, "存储清除失败");
    }
}

void BL0906Factory::force_recover_calibration_data() {
    ESP_LOGI(FACTORY_TAG, "开始强制恢复校准数据...");

    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建存储对象失败");
        return;
    }

    // 检查是否是preference存储类型
    if (storage->get_storage_type() == "preference") {
        auto pref_storage = static_cast<PreferenceCalibrationStorage*>(storage.get());
        if (pref_storage) {
            ESP_LOGI(FACTORY_TAG, "开始强制数据恢复...");

            if (pref_storage->force_data_recovery()) {
                ESP_LOGI(FACTORY_TAG, "✅ 强制数据恢复成功！");

                // 显示恢复的数据
                show_all_instances_calibration_data();

                // 如果当前实例的数据被恢复，尝试加载到芯片
                uint32_t current_instance = this->get_instance_id();
                std::vector<CalibrationEntry> entries;
                if (storage->read_instance(current_instance, entries)) {
                    ESP_LOGI(FACTORY_TAG, "当前实例 0x%08X 的数据已恢复，正在应用到芯片...", current_instance);

                    if (this->reload_calibration_data_to_chip()) {
                        ESP_LOGI(FACTORY_TAG, "✅ 校准数据已成功应用到芯片");
                    } else {
                        ESP_LOGW(FACTORY_TAG, "⚠️ 校准数据恢复成功，但应用到芯片失败");
                    }
                }
            } else {
                ESP_LOGE(FACTORY_TAG, "❌ 强制数据恢复失败，没有找到任何可恢复的数据");
            }
        } else {
            ESP_LOGE(FACTORY_TAG, "存储类型转换失败");
        }
    } else {
        ESP_LOGW(FACTORY_TAG, "当前存储类型 (%s) 不支持强制恢复功能", storage->get_storage_type().c_str());
    }
}

void BL0906Factory::diagnose_nvs_storage() {
    ESP_LOGI(FACTORY_TAG, "开始NVS存储诊断...");

    // 使用配置好的存储实例
    auto storage = this->create_storage_instance();
    if (!storage) {
        ESP_LOGE(FACTORY_TAG, "创建存储对象失败");
        return;
    }

    // 检查是否是preference存储类型
    if (storage->get_storage_type() == "preference") {
        auto pref_storage = static_cast<PreferenceCalibrationStorage*>(storage.get());
        if (pref_storage) {
            uint32_t current_instance = this->get_instance_id();
            ESP_LOGI(FACTORY_TAG, "诊断当前实例: 0x%08X", current_instance);

            pref_storage->diagnose_nvs_storage(current_instance);
        } else {
            ESP_LOGE(FACTORY_TAG, "存储类型转换失败");
        }
    } else {
        ESP_LOGW(FACTORY_TAG, "当前存储类型 (%s) 不支持NVS诊断功能", storage->get_storage_type().c_str());
    }
}

bool BL0906Factory::reload_calibration_data_to_chip() {
    ESP_LOGI(FACTORY_TAG, "重新加载校准数据到芯片...");
    return load_calibration_data();
}

bool BL0906Factory::load_calibration_data() {
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "校准存储未初始化");
        return false;
    }
    
    std::vector<CalibrationEntry> entries;
    if (!calibration_storage_->read_instance(instance_id_, entries)) {
        ESP_LOGD(FACTORY_TAG, "实例 0x%08X 的校准数据不存在", instance_id_);
        return false;
    }
    
    ESP_LOGI(FACTORY_TAG, "从存储加载 %d 个校准值", entries.size());
    
    // 解除写保护
    if (!this->turn_off_write_protect()) {
        ESP_LOGE(FACTORY_TAG, "无法解除写保护");
        return false;
    }
    
    // 应用校准值到芯片
    int success_count = 0;
    for (const auto& entry : entries) {
        // 统一使用write_register_value方法，适配器已正确处理所有寄存器类型的符号扩展
        bool write_success = write_register_value(entry.register_addr, entry.value);
        ESP_LOGD(FACTORY_TAG, "应用校准值: 寄存器0x%02X = %d", 
                 entry.register_addr, entry.value);
        
        if (write_success) {
            success_count++;
        }
        
                // 防止看门狗复位
        App.feed_wdt();
        delay(2); // 短暂延时
    }
    
    ESP_LOGI(FACTORY_TAG, "校准值应用完成：%d 成功，%d 失败", 
             success_count, (int)(entries.size() - success_count));
    
    return success_count > 0;
}

bool BL0906Factory::save_calibration_data() {
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "校准存储未初始化");
        return false;
    }
    
#ifdef BL0906_CALIBRATION_MODE
    std::vector<CalibrationEntry> entries;
    
    // 从所有已注册的Number组件收集当前值
    for (auto* number : calib_numbers_) {
        if (number != nullptr) {
            CalibrationEntry entry;
            entry.register_addr = number->get_register_address();
            entry.value = static_cast<int16_t>(number->state);
            entries.push_back(entry);
            
            ESP_LOGD(FACTORY_TAG, "收集校准值: 寄存器0x%02X = %d", 
                     entry.register_addr, entry.value);
        }
    }
    
    if (entries.empty()) {
        ESP_LOGW(FACTORY_TAG, "没有校准值需要保存");
        return false;
    }
    
    if (calibration_storage_->write_instance(instance_id_, entries)) {
        ESP_LOGI(FACTORY_TAG, "成功保存 %d 个校准值", entries.size());
        return true;
    } else {
        ESP_LOGE(FACTORY_TAG, "保存校准数据失败");
        return false;
    }
#else
    ESP_LOGW(FACTORY_TAG, "量产版本不支持保存校准数据");
    return false;
#endif
}

// 批量修改所有CHGN值的方法实现
void BL0906Factory::update_all_chgn_values_from_sensor() {
  if (!chgn_reference_sensor_) {
    ESP_LOGE(FACTORY_TAG, "CHGN参考传感器未设置，无法批量修改CHGN值");
    return;
  }
  
  // 检查传感器是否有有效的状态
  if (std::isnan(chgn_reference_sensor_->state)) {
    ESP_LOGE(FACTORY_TAG, "CHGN参考传感器状态无效，无法批量修改CHGN值");
    return;
  }
  
  // 获取参考值并转换为int16_t（CHGN寄存器为16位补码）
  int16_t reference_value = static_cast<int16_t>(chgn_reference_sensor_->state);
  
  ESP_LOGI(FACTORY_TAG, "开始批量修改所有CHGN值，参考值: %d", reference_value);
  
  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，无法修改CHGN值");
    return;
  }
  
  int success_count = 0;
  int total_count = 0;
  
  // 批量修改6个电流通道的CHGN值
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint8_t address = CHGN_ADDR[i];
    total_count++;
    
    if (this->write_register_value(address, reference_value)) {
      ESP_LOGI(FACTORY_TAG, "通道%d CHGN(0x%02X) 修改成功: %d", i+1, address, reference_value);
      success_count++;
    } else {
      ESP_LOGE(FACTORY_TAG, "通道%d CHGN(0x%02X) 修改失败", i+1, address);
    }
    
    // 防止看门狗复位并允许其他任务运行
    App.feed_wdt();
    delay(5);  // 短暂延时避免连续写入过快
  }
  
  // 修改电压通道的CHGN值
  total_count++;
  if (this->write_register_value(BL0906_CHGN_V, reference_value)) {
    ESP_LOGI(FACTORY_TAG, "电压通道 CHGN_V(0x%02X) 修改成功: %d", BL0906_CHGN_V, reference_value);
    success_count++;
  } else {
    ESP_LOGE(FACTORY_TAG, "电压通道 CHGN_V(0x%02X) 修改失败", BL0906_CHGN_V);
  }
  
  ESP_LOGI(FACTORY_TAG, "批量修改CHGN值完成: %d 成功，%d 失败 (总计%d)", 
           success_count, total_count - success_count, total_count);
  
  // 刷新所有校准数字组件显示
  refresh_all_calib_numbers();
  
  if (success_count == total_count) {
    ESP_LOGI(FACTORY_TAG, "✅ 所有CHGN值批量修改成功");
  } else {
    ESP_LOGW(FACTORY_TAG, "⚠️ 部分CHGN值修改失败，请检查通信状态");
  }
}

// 批量将所有CHGN值清零的方法实现
void BL0906Factory::reset_all_chgn_values_to_zero() {
  ESP_LOGI(FACTORY_TAG, "开始批量清零所有CHGN值");
  
  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，无法清零CHGN值");
    return;
  }
  
  int success_count = 0;
  int total_count = 0;
  const int16_t zero_value = 0;
  
  // 批量清零6个电流通道的CHGN值
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint8_t address = CHGN_ADDR[i];
    total_count++;
    
    if (this->write_register_value(address, zero_value)) {
      ESP_LOGI(FACTORY_TAG, "通道%d CHGN(0x%02X) 清零成功", i+1, address);
      success_count++;
    } else {
      ESP_LOGE(FACTORY_TAG, "通道%d CHGN(0x%02X) 清零失败", i+1, address);
    }
    
    // 防止看门狗复位并允许其他任务运行
    App.feed_wdt();
    delay(5);  // 短暂延时避免连续写入过快
  }
  
  // 清零电压通道的CHGN值
  total_count++;
  if (this->write_register_value(BL0906_CHGN_V, zero_value)) {
    ESP_LOGI(FACTORY_TAG, "电压通道 CHGN_V(0x%02X) 清零成功", BL0906_CHGN_V);
    success_count++;
  } else {
    ESP_LOGE(FACTORY_TAG, "电压通道 CHGN_V(0x%02X) 清零失败", BL0906_CHGN_V);
  }
  
  ESP_LOGI(FACTORY_TAG, "批量清零CHGN值完成: %d 成功，%d 失败 (总计%d)", 
           success_count, total_count - success_count, total_count);
  
  // 刷新所有校准数字组件显示
  refresh_all_calib_numbers();
  
  if (success_count == total_count) {
    ESP_LOGI(FACTORY_TAG, "✅ 所有CHGN值批量清零成功");
  } else {
    ESP_LOGW(FACTORY_TAG, "⚠️ 部分CHGN值清零失败，请检查通信状态");
  }
}

// 批量将所有RMSOS值清零的方法实现
void BL0906Factory::reset_all_rmsos_values_to_zero() {
  ESP_LOGI(FACTORY_TAG, "开始批量清零所有RMSOS值");
  
  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，无法清零RMSOS值");
    return;
  }
  
  int success_count = 0;
  int total_count = 0;
  const uint32_t zero_value = 0;  // RMSOS是24位寄存器，使用32位变量
  
  // 批量清零6个电流通道的RMSOS值（注意：RMSOS没有电压通道）
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint8_t address = RMSOS_ADDR[i];
    total_count++;
    
    if (this->write_register_24bit(address, zero_value)) {
      ESP_LOGI(FACTORY_TAG, "通道%d RMSOS(0x%02X) 清零成功", i+1, address);
      success_count++;
    } else {
      ESP_LOGE(FACTORY_TAG, "通道%d RMSOS(0x%02X) 清零失败", i+1, address);
    }
    
    // 防止看门狗复位并允许其他任务运行
    App.feed_wdt();
    delay(5);  // 短暂延时避免连续写入过快
  }
  
  ESP_LOGI(FACTORY_TAG, "批量清零RMSOS值完成: %d 成功，%d 失败 (总计%d)", 
           success_count, total_count - success_count, total_count);
  
  // 刷新所有校准数字组件显示
  refresh_all_calib_numbers();
  
  if (success_count == total_count) {
    ESP_LOGI(FACTORY_TAG, "✅ 所有RMSOS值批量清零成功");
  } else {
    ESP_LOGW(FACTORY_TAG, "⚠️ 部分RMSOS值清零失败，请检查通信状态");
  }
}

// 旧的SPI方法已被通信适配器替代，不再需要

#endif  // USE_BL0906_FACTORY_SPI

// ========== 频率适配功能实现 ==========

// 频率检测方法
float BL0906Factory::detect_grid_frequency() {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置，无法检测频率");
    return 0.0f;
  }
  
  float freq_sum = 0.0f;
  int valid_samples = 0;
  
  ESP_LOGI(FACTORY_TAG, "开始采样检测频率，采样次数: %d", FREQ_DETECTION_SAMPLES);
  
  for (int i = 0; i < FREQ_DETECTION_SAMPLES; i++) {
    bool success = false;
    int32_t freq_raw = send_read_command_and_receive(BL0906_FREQUENCY, &success);
    
    if (success && freq_raw > 0) {
      float freq = convert_raw_to_value(BL0906_FREQUENCY, freq_raw);
      if (freq > 30.0f && freq < 80.0f) {  // 合理的频率范围
        freq_sum += freq;
        valid_samples++;
        ESP_LOGD(FACTORY_TAG, "频率采样 %d: %.2fHz (原始值: %d)", 
                i + 1, freq, freq_raw);
      }
    }
    
    // 采样间隔
    delay(100);
  }
  
  if (valid_samples > 0) {
    float avg_freq = freq_sum / valid_samples;
    ESP_LOGI(FACTORY_TAG, "频率检测完成: %.2fHz (有效采样: %d/%d)", 
            avg_freq, valid_samples, FREQ_DETECTION_SAMPLES);
    return avg_freq;
  }
  
  ESP_LOGE(FACTORY_TAG, "频率检测失败：无有效采样");
  return 0.0f;
}

// 设置AC频率模式
bool BL0906Factory::set_ac_frequency_mode(bool is_60hz) {
  ESP_LOGI(FACTORY_TAG, "设置AC频率模式为: %s", is_60hz ? "60Hz" : "50Hz");
  
  // 解除写保护
  if (!turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护");
    return false;
  }
  
  // 读取当前MODE2寄存器值
  uint32_t current_mode2 = read_mode2_register();
  if (current_mode2 == 0xFFFFFFFF) {  // 读取失败标志
    ESP_LOGE(FACTORY_TAG, "读取MODE2寄存器失败");
    return false;
  }
  
  // 修改AC_FREQ_SEL位
  uint32_t new_mode2 = current_mode2;
  if (is_60hz) {
    new_mode2 |= MODE2_AC_FREQ_SEL_MASK;   // 设置第23位为1
  } else {
    new_mode2 &= ~MODE2_AC_FREQ_SEL_MASK;  // 清除第23位为0
  }
  
  // 如果值没有变化，直接返回成功
  if (new_mode2 == current_mode2) {
    ESP_LOGI(FACTORY_TAG, "频率模式已经是%s，无需修改", is_60hz ? "60Hz" : "50Hz");
    return true;
  }
  
  // 写入新值
  if (write_mode2_register(new_mode2)) {
    ESP_LOGI(FACTORY_TAG, "MODE2寄存器更新成功: 0x%06X -> 0x%06X", 
            current_mode2, new_mode2);
    return true;
  } else {
    ESP_LOGE(FACTORY_TAG, "MODE2寄存器写入失败");
    return false;
  }
}

// 读取MODE2寄存器
uint32_t BL0906Factory::read_mode2_register() {
  bool success = false;
  int32_t raw_value = send_read_command_and_receive(BL0906_MODE2, &success);
  
  if (success) {
    uint32_t mode2_value = static_cast<uint32_t>(raw_value);
    ESP_LOGD(FACTORY_TAG, "读取MODE2寄存器: 0x%06X", mode2_value);
    return mode2_value;
  } else {
    ESP_LOGE(FACTORY_TAG, "读取MODE2寄存器失败");
    return 0xFFFFFFFF;  // 返回失败标志
  }
}

// 写入MODE2寄存器
bool BL0906Factory::write_mode2_register(uint32_t value) {
  ESP_LOGD(FACTORY_TAG, "写入MODE2寄存器: 0x%06X", value);
  return write_register_24bit(BL0906_MODE2, value);
}

// ========== 重构后的一次性读取方法实现 ==========

/**
 * 读取所有通道数据
 * @return true 如果至少33%的通道读取成功
 */
bool BL0906Factory::read_all_channels_data() {
  int successful_channels = 0;
  
  ESP_LOGV(FACTORY_TAG, "开始读取所有通道数据 (%s)", CHIP_MODEL_NAME);
  
  for (int channel = 0; channel < MAX_CHANNELS; channel++) {
    if (read_single_channel_data(channel)) {
      successful_channels++;
    }
  }
  
  float success_rate = static_cast<float>(successful_channels) / MAX_CHANNELS;
  ESP_LOGV(FACTORY_TAG, "通道读取完成: %d/%d成功 (%.1f%%)", 
           successful_channels, MAX_CHANNELS, success_rate * 100);
           
  // 降低成功率要求：至少33%成功即可，避免因个别通道问题导致整体失败
  return success_rate >= 0.33f;  // 至少33%成功
}

/**
 * 读取单个通道的完整数据
 * @param channel 通道编号 (0-5)
 * @return true 如果至少有一个数据读取成功
 */
bool BL0906Factory::read_single_channel_data(int channel) {
  if (!is_valid_channel(channel)) {
    ESP_LOGW(FACTORY_TAG, "无效的通道编号: %d", channel);
    return false;
  }
  
  int successful_reads = 0;
  
  // 读取电流（使用条件编译的地址数组）
  bool success = false;
  int32_t current_raw = send_read_command_and_receive(I_RMS_ADDR[channel], &success);
  current_data_.channels[channel].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
  if (success) {
    successful_reads++;
    ESP_LOGV(FACTORY_TAG, "通道%d电流读取成功: %u", channel + 1, current_data_.channels[channel].current_raw);
  } else {
    ESP_LOGD(FACTORY_TAG, "通道%d电流读取失败", channel + 1);
  }
  
  // 读取功率（使用条件编译的地址数组）
  current_data_.channels[channel].power_raw = send_read_command_and_receive(WATT_ADDR[channel], &success);
  if (success) {
    successful_reads++;
    ESP_LOGV(FACTORY_TAG, "通道%d功率读取成功: %d", channel + 1, current_data_.channels[channel].power_raw);
  } else {
    current_data_.channels[channel].power_raw = 0;
    ESP_LOGD(FACTORY_TAG, "通道%d功率读取失败", channel + 1);
  }
  
  // 读取电量（使用条件编译的地址数组）
  int32_t energy_raw = send_read_command_and_receive(CF_CNT_ADDR[channel], &success);
  current_data_.channels[channel].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
  if (success) {
    successful_reads++;
    ESP_LOGV(FACTORY_TAG, "通道%d电量读取成功: %u", channel + 1, current_data_.channels[channel].energy_raw);
  } else {
    ESP_LOGD(FACTORY_TAG, "通道%d电量读取失败", channel + 1);
  }
  
  bool channel_success = successful_reads > 0;
  ESP_LOGV(FACTORY_TAG, "通道%d读取结果: %d/3个成功 (%s)", 
           channel + 1, successful_reads, channel_success ? "成功" : "失败");
  
  return channel_success;  // 至少有一个数据读取成功即可
}

// 通用的24位寄存器写入方法
bool BL0906Factory::write_register_24bit(uint8_t address, uint32_t value) {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置，无法写入24位寄存器");
    return false;
  }
  
  // 确保值在24位范围内
  if (value > 0xFFFFFF) {
    ESP_LOGE(FACTORY_TAG, "24位寄存器值超出范围: 0x%08X", value);
    return false;
  }
  
  ESP_LOGD(FACTORY_TAG, "写入24位寄存器 0x%02X: 0x%06X", address, value);
  
  // 构造24位写入命令
  std::string adapter_type = comm_adapter_->get_adapter_type();
  uint8_t write_cmd[6];
  
  if (adapter_type == "UART") {
    // UART写命令格式: [写命令] [地址] [低字节] [中字节] [高字节] [校验和]
    write_cmd[0] = 0xCA;  // UART写命令
    write_cmd[1] = address;  // 寄存器地址
    write_cmd[2] = value & 0xFF;  // 低字节
    write_cmd[3] = (value >> 8) & 0xFF;  // 中字节
    write_cmd[4] = (value >> 16) & 0xFF;  // 高字节
    // 计算校验和
    write_cmd[5] = (write_cmd[0] + write_cmd[1] + write_cmd[2] + write_cmd[3] + write_cmd[4]) ^ 0xFF;
  } else if (adapter_type == "SPI") {
    // SPI写命令格式: [写命令] [地址] [高字节] [中字节] [低字节] [校验和]
    write_cmd[0] = 0x81;  // SPI写命令
    write_cmd[1] = address;  // 寄存器地址
    write_cmd[2] = (value >> 16) & 0xFF;  // 高字节
    write_cmd[3] = (value >> 8) & 0xFF;   // 中字节
    write_cmd[4] = value & 0xFF;          // 低字节
    // 计算校验和
    write_cmd[5] = (write_cmd[0] + write_cmd[1] + write_cmd[2] + write_cmd[3] + write_cmd[4]) ^ 0xFF;
  } else {
    ESP_LOGE(FACTORY_TAG, "未知的适配器类型: %s", adapter_type.c_str());
    return false;
  }
  
  ESP_LOGD(FACTORY_TAG, "24位寄存器写入命令: %02X %02X %02X %02X %02X %02X", 
           write_cmd[0], write_cmd[1], write_cmd[2], write_cmd[3], write_cmd[4], write_cmd[5]);
  
  // 发送写入命令
  bool success = comm_adapter_->send_raw_command(write_cmd, 6);
  
  if (success) {
    ESP_LOGD(FACTORY_TAG, "24位寄存器 0x%02X 写入成功", address);
  } else {
    ESP_LOGE(FACTORY_TAG, "24位寄存器 0x%02X 写入失败: %s", address, comm_adapter_->get_last_error().c_str());
  }
  
  return success;
}

}  // namespace bl0906_factory
}  // namespace esphome
