# BL0906 电网频率自动适配功能

## 功能概述

BL0906Factory组件现在支持电网频率自动适配功能，可以在初始化时自动检测电网频率并设置相应的工作模式，或者强制设置为60Hz模式。

## 技术原理

- **寄存器地址**: 0x97 MODE2 工作模式寄存器
- **位字段**: [23] AC_FREQ_SEL（交流电频率选择）
  - 1 = 60Hz
  - 0 = 50Hz（默认）
- **检测逻辑**: 如果检测到频率在55-65Hz之间，自动设置为60Hz模式

## 配置选项

### 1. 禁用频率适配（默认）
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: off  # 禁用频率适配，使用芯片默认50Hz模式
```

### 2. 自动频率检测
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: auto  # 自动检测频率并设置
```

### 3. 强制设置为60Hz
```yaml
bl0906_factory:
  # ... 其他配置 ...
  freq_adapt: 60  # 不检测，直接设置为60Hz模式
```

## 工作流程

### 自动检测模式 (auto)
1. 在组件初始化时，读取BL0906的频率寄存器
2. 进行3次采样，计算平均频率
3. 如果平均频率在55-65Hz范围内，设置为60Hz模式
4. 否则保持默认的50Hz模式
5. 记录检测结果到日志

### 强制60Hz模式 (60)
1. 在组件初始化时，直接设置MODE2寄存器的AC_FREQ_SEL位为1
2. 跳过频率检测过程，节省启动时间

### 禁用模式 (off)
1. 不进行任何频率相关操作
2. 芯片使用默认的50Hz模式

## 日志输出示例

### 自动检测模式日志
```
[I][bl0906_factory:XXX] 开始电网频率自动检测...
[I][bl0906_factory:XXX] 开始采样检测频率，采样次数: 3
[D][bl0906_factory:XXX] 频率采样 1: 59.98Hz (原始值: 166722)
[D][bl0906_factory:XXX] 频率采样 2: 60.01Hz (原始值: 166639)
[D][bl0906_factory:XXX] 频率采样 3: 59.99Hz (原始值: 166681)
[I][bl0906_factory:XXX] 频率检测完成: 59.99Hz (有效采样: 3/3)
[I][bl0906_factory:XXX] 检测到电网频率: 59.99Hz，设置为60Hz模式
[D][bl0906_factory:XXX] 读取MODE2寄存器: 0x000000
[D][bl0906_factory:XXX] MODE2写入命令: CA 97 00 00 80 08
[D][bl0906_factory:XXX] MODE2寄存器写入成功
[I][bl0906_factory:XXX] MODE2寄存器更新成功: 0x000000 -> 0x800000
[I][bl0906_factory:XXX] 电网频率自动适配完成
```

### 强制60Hz模式日志
```
[I][bl0906_factory:XXX] 强制设置为60Hz模式...
[D][bl0906_factory:XXX] 读取MODE2寄存器: 0x000000
[D][bl0906_factory:XXX] MODE2写入命令: CA 97 00 00 80 08
[D][bl0906_factory:XXX] MODE2寄存器写入成功
[I][bl0906_factory:XXX] MODE2寄存器更新成功: 0x000000 -> 0x800000
[I][bl0906_factory:XXX] 强制60Hz模式设置完成
```

## 性能影响

- **自动检测模式**: 增加约300-500ms的启动时间（3次采样 × 100ms间隔）
- **强制60Hz模式**: 增加约10-20ms的启动时间（仅寄存器写入）
- **禁用模式**: 无性能影响

## 错误处理

1. **频率检测失败**: 自动回退到默认50Hz模式，不影响正常功能
2. **寄存器写入失败**: 记录错误日志，使用默认50Hz模式
3. **通信适配器未设置**: 跳过频率适配，记录错误日志

## 兼容性

- 向后兼容：现有配置无需修改，默认为禁用模式
- 支持UART和SPI通信方式
- 适用于所有BL0906芯片版本

## 注意事项

1. 频率适配只在首次初始化时执行，避免运行时频繁修改
2. 检测参数已硬编码，无需用户配置
3. 失败时自动回退到安全的默认50Hz模式
4. 建议在60Hz电网地区使用自动检测或强制60Hz模式
5. 在50Hz电网地区可以使用禁用模式以节省启动时间

### 寄存器位宽修正
在实现频率适配功能时，发现并修正了RMSOS寄存器的位宽问题：

- **问题**: RMSOS寄存器（地址0x78-0x81）是24位寄存器，但之前的代码使用16位写入方法
- **修正**: 
  - 新增了通用的`write_register_24bit()`方法
  - 新增了`is_24bit_register()`判断方法
  - 修正了RMSOS相关的所有写入操作：
    - `calculate_rmsos_from_current()` - RMSOS自动计算
    - `reset_all_rmsos_values_to_zero()` - RMSOS批量清零
    - `load_calibration_data()` - 校准数据加载
    - `apply_calibration_values()` - 初始校准值应用

### 24位寄存器列表
目前支持的24位寄存器：
- **RMSOS系列**: 0x78-0x81 (RMSOS_1-6, RMSOS_V)
- **MODE2**: 0x97 (工作模式寄存器2)

### 兼容性说明
- 修正后的代码向后兼容，不影响现有的16位寄存器操作
- 校准数据存储格式保持不变（仍为16位），但写入时会自动扩展为24位
- 所有24位寄存器的写入都会自动选择正确的写入方法

## 技术细节

### 24位寄存器写入格式

#### UART模式
```
[写命令] [地址] [低字节] [中字节] [高字节] [校验和]
0xCA     0x78   0x00     0x10     0x00     0x??
```

#### SPI模式  
```
[写命令] [地址] [高字节] [中字节] [低字节] [校验和]
0x81     0x78   0x00     0x10     0x00     0x??
```

### 数据转换
- 16位校准值 → 24位无符号值：`uint32_t value_24bit = static_cast<uint32_t>(static_cast<uint16_t>(value))`
- 24位有符号范围：-8388608 到 8388607
- 写入时转换为无符号：`value & 0xFFFFFF`

## 测试配置示例

参考项目中的测试配置文件：
- `test_freq_adapt.yaml` - 自动检测模式测试
- `test_freq_60hz.yaml` - 强制60Hz模式测试 