# BL0906校准数据恢复按钮配置
# 用于在数据丢失时强制恢复校准数据

button:
  # 强制恢复校准数据按钮
  - platform: template
    name: "Force Recover Calibration Data"
    id: force_recover_calib_data
    icon: "mdi:backup-restore"
    on_press:
      then:
        - logger.log:
            format: "开始强制恢复校准数据..."
            level: INFO
        - lambda: |-
            if (id(bl0906_factory_component)) {
              id(bl0906_factory_component)->force_recover_calibration_data();
            } else {
              ESP_LOGE("button", "BL0906工厂组件未找到");
            }

  # 数据完整性检查按钮
  - platform: template
    name: "Check Data Integrity"
    id: check_data_integrity
    icon: "mdi:shield-check"
    on_press:
      then:
        - logger.log:
            format: "检查数据完整性..."
            level: INFO
        - lambda: |-
            if (id(bl0906_factory_component)) {
              // 显示存储状态
              id(bl0906_factory_component)->show_storage_status();
              // 显示所有实例数据
              id(bl0906_factory_component)->show_all_instances_calibration_data();
            } else {
              ESP_LOGE("button", "BL0906工厂组件未找到");
            }

  # 重新加载校准数据按钮
  - platform: template
    name: "Reload Calibration Data"
    id: reload_calib_data
    icon: "mdi:reload"
    on_press:
      then:
        - logger.log:
            format: "重新加载校准数据到芯片..."
            level: INFO
        - lambda: |-
            if (id(bl0906_factory_component)) {
              if (id(bl0906_factory_component)->load_calibration_data()) {
                ESP_LOGI("button", "✅ 校准数据重新加载成功");
              } else {
                ESP_LOGE("button", "❌ 校准数据重新加载失败");
              }
            } else {
              ESP_LOGE("button", "BL0906工厂组件未找到");
            }

# 诊断传感器
text_sensor:
  - platform: template
    name: "Calibration Data Status"
    id: calib_data_status
    icon: "mdi:information"
    update_interval: 30s
    lambda: |-
      if (id(bl0906_factory_component)) {
        auto storage = id(bl0906_factory_component)->create_storage_instance();
        if (storage) {
          auto instance_list = storage->get_instance_list();
          uint32_t current_instance = id(bl0906_factory_component)->get_instance_id();
          
          std::string status = "实例数: " + to_string(instance_list.size());
          status += ", 当前ID: 0x" + format_hex(current_instance);
          
          // 检查当前实例是否存在
          bool current_exists = false;
          for (uint32_t id : instance_list) {
            if (id == current_instance) {
              current_exists = true;
              break;
            }
          }
          
          if (current_exists) {
            status += " ✅";
          } else {
            status += " ❌";
          }
          
          return status;
        }
      }
      return std::string("未知");

# 自动恢复逻辑（可选）
interval:
  - interval: 60s
    then:
      - lambda: |-
          // 每分钟检查一次数据完整性
          if (id(bl0906_factory_component)) {
            auto storage = id(bl0906_factory_component)->create_storage_instance();
            if (storage) {
              auto instance_list = storage->get_instance_list();
              uint32_t current_instance = id(bl0906_factory_component)->get_instance_id();
              
              // 检查当前实例是否存在
              bool current_exists = false;
              for (uint32_t id : instance_list) {
                if (id == current_instance) {
                  current_exists = true;
                  break;
                }
              }
              
              if (!current_exists && instance_list.empty()) {
                ESP_LOGW("auto_recovery", "检测到校准数据丢失，尝试自动恢复...");
                id(bl0906_factory_component)->force_recover_calibration_data();
              }
            }
          }
