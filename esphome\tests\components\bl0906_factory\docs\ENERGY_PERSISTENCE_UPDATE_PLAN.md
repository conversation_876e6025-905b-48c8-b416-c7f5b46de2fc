# 电量持久化逻辑更新计划

## 1. 修改目标
根据最新确认的总和校验机制，统一采用实时读取总和与上次保存总和的差值判断触发保存，移除旧缓存与闪存总和比较逻辑。

## 2. 核心修改点
- 新增 `saved_total_cf` 变量记录每次保存时的6通道CF总和
- 采用 `current_total_cf - saved_total_cf ≥ 1000` 作为主要触发条件
- 保留 `last_cf_count_needs_save_` 标志位作为辅助触发条件
- 移除原缓存与闪存中 `persistent_cf_count` 总和比较逻辑

## 3. 文件修改列表
| 文件名 | 路径 | 修改类型 |
|--------|------|----------|
| bl0906_factory.h | ./bl0906_factory.h | 新增成员变量声明 |
| bl0906_factory.cpp | ./bl0906_factory.cpp | 修改保存触发逻辑、新增总和计算 |

## 4. 详细修改步骤
### 4.1 bl0906_factory.h
```cpp
// 在类定义中新增成员变量
bool last_cf_count_needs_save_;
uint32_t saved_total_cf;  // 新增：保存上次保存时的6通道CF总和
std::array<uint32_t, MAX_CHANNELS> persistent_cf_count_;
std::array<uint32_t, MAX_CHANNELS> last_cf_count_;
```

### 4.2 bl0906_factory.cpp
#### 4.2.1 初始化函数
```cpp
void BL0906Factory::setup_energy_persistence() {
  // 原有初始化逻辑...
  saved_total_cf = 0;  // 初始化总和变量
}
```

#### 4.2.2 总和计算逻辑
```cpp
void BL0906Factory::process_energy_persistence() {
  uint32_t current_total_cf = 0;  // 计算当前总和
  for (size_t i = 0; i < channels_.size(); i++) {
    // 原有通道处理逻辑...
    current_total_cf += current_count;  // 累加当前通道CF值
  }

  // 修改保存触发条件
  bool need_save = (current_total_cf - saved_total_cf >= 1000) || last_cf_count_needs_save_;
  if (need_save) {
    save_energy_data();
    saved_total_cf = current_total_cf;  // 更新保存总和
    last_cf_count_needs_save_ = false;
  }
}
```

#### 4.2.3 移除旧逻辑
删除以下代码块：
```cpp
// 移除缓存与闪存总和比较逻辑
uint32_t cache_sum = 0, flash_sum = 0;
for (size_t i = 0; i < MAX_CHANNELS; i++) {
  cache_sum += persistent_cf_count_[i];
  flash_sum += loaded_data.persistent_cf_count[i];
}
if (abs(cache_sum - flash_sum) > MIN_CHANGE_THRESHOLD) {
  // 原有保存逻辑...
}
```

## 5. 验证方案
1. 模拟6通道CF值累计，当总和超过1000时触发保存
2. 测试芯片重启场景下 `last_cf_count_needs_save_` 标志位触发保存
3. 验证 `saved_total_cf` 在每次保存后正确更新

## 6. 注意事项
- 无需向后兼容，可直接删除旧总和校验相关代码
- 确保 `saved_total_cf` 在 `save_energy_data()` 成功后才更新
- 通道数量通过 `channels_.size()` 动态获取，不硬编码6通道