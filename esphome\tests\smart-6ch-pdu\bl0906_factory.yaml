substitutions:
  name: "smart-pdu-6-ch"
  friendly_name: "smart-pdu-6-ch"
  ch1: "ch_1"   #可根据需要修改名称
  ch2: "ch_2"   #可根据需要修改名称
  ch3: "ch_3"    #可根据需要修改名称
  ch4: "ch_4"    #可根据需要修改名称
  ch5: "ch_5"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
  ch1_disp: "电脑"   #可根据需要修改名称
  ch2_disp: "R730"   #可根据需要修改名称
  ch3_disp: "交换机"    #可根据需要修改名称
  ch4_disp: "NAS"    #可根据需要修改名称
  ch5_disp: "ch_5"  #可根据需要修改名称
  ch6_disp: "ch_6"     #可根据需要修改名称
globals:
  - id: ch1_energy_
    type: float
    restore_value: yes
  - id: ch1_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: ch2_energy_
    type: float
    restore_value: yes
  - id: ch2_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: ch3_energy_
    type: float
    restore_value: yes
  - id: ch3_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: ch4_energy_
    type: float
    restore_value: yes
  - id: ch4_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: ch5_energy_
    type: float
    restore_value: yes
  - id: ch5_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: ch6_energy_
    type: float
    restore_value: yes
  - id: ch6_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chs6_energy_
    type: float
    restore_value: yes
  - id: chs6_energy_last   #energy from last power cycle
    type: float
    restore_value: yes
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
bl0906_factory:
    id: sensor_bl0906
    uart_id: uart_bus
    update_interval: 20s

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    frequency:
      name: 'BL0906 Frequency'
      on_value:
        - lvgl.label.update:
              id: frequency_label
              text:
                format: "%.1f Hz"
                args: ['x']
    temperature:
      name: 'BL0906 Temperature'
    voltage:
      name: 'BL0906 Voltage'
      on_value:
        - lvgl.label.update:
              id: voltage_label
              text:
                format: "%.1f V"
                args: ['x']
    current_1:
      name: "${ch1} current"
      on_value:
        - lvgl.label.update:
            id: ch1_current_label
            text:
              format: "%.2fA"
              args: ['x']
    current_2:
      name: "${ch2} current"
      on_value:
        - lvgl.label.update:
            id: ch2_current_label
            text:
              format: "%.2fA"
              args: ['x']
    current_3:
      name: "${ch3} current"
      on_value:
        - lvgl.label.update:
            id: ch3_current_label
            text:
              format: "%.2fA"
              args: ['x']
    current_4:
      name: "${ch4} current"
      on_value:
        - lvgl.label.update:
            id: ch4_current_label
            text:
              format: "%.2fA"
              args: ['x']
    current_5:
      name: "${ch5} current"
      on_value:
        - lvgl.label.update:
            id: ch5_current_label
            text:
              format: "%.2fA"
              args: ['x']
    current_6:
      name: "${ch6} current"
      on_value:
        - lvgl.label.update:
            id: ch6_current_label
            text:
              format: "%.2fA"
              args: ['x']
    power_1:
      name: "${ch1} power"
      on_value:
        - lvgl.label.update:
            id: ch1_power_label
            text:
              format: "%.1fW"
              args: ['x']
        - lvgl.indicator.update:
            id: power_needle_ch1
            value: !lambda return x;
              # format: "%.0f w"
              # args: ['x']
        - lvgl.label.update:
            id: ch1_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_2:
      name: "${ch2} power"
      on_value:
        - lvgl.label.update:
            id: ch2_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_3:
      name: "${ch3} power"
      on_value:
        - lvgl.label.update:
            id: ch3_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_4:
      name: "${ch4} power"
      on_value:
        - lvgl.label.update:
            id: ch4_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_5:
      name: "${ch5} power"
      on_value:
        - lvgl.label.update:
            id: ch5_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_6:
      name: "${ch6} power"
      on_value:
        - lvgl.label.update:
            id: ch6_power_label
            text:
              format: "%.1fW"
              args: ['x']
    power_sum:
      name: "6-ch sum power"
      on_value:
        - lvgl.label.update:
              id: total_power_label
              text:
                format: "%.2f w"
                args: ['x']
    energy_1:
      id: ch1_energy
      name: "${ch1} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch1_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch1_energy_
                  value: !lambda return id(ch1_energy_last) + x;
              else:
                - globals.set:
                    id: ch1_energy_last
                    value: !lambda return id(ch1_energy_);
          # - lvgl.label.update:
          #     id: ch1_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']
    energy_2:
      id: ch2_energy
      name: "${ch2} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch2_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch2_energy_
                  value: !lambda return id(ch2_energy_last) + x;
              else:
                - globals.set:
                    id: ch2_energy_last
                    value: !lambda return id(ch2_energy_);
          # - lvgl.label.update:
          #     id: ch2_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']
    energy_3:
      id: ch3_energy
      name: "${ch3} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch3_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch3_energy_
                  value: !lambda return id(ch3_energy_last) + x;
              else:
                - globals.set:
                    id: ch3_energy_last
                    value: !lambda return id(ch3_energy_);
          # - lvgl.label.update:
          #     id: ch3_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']
    energy_4:
      id: ch4_energy
      name: "${ch4} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch4_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch4_energy_
                  value: !lambda return id(ch4_energy_last) + x;
              else:
                - globals.set:
                    id: ch4_energy_last
                    value: !lambda return id(ch4_energy_);
          # - lvgl.label.update:
          #     id: ch4_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']
    energy_5:
      id: ch5_energy
      name: "${ch5} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch5_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch5_energy_
                  value: !lambda return id(ch5_energy_last) + x;
              else:
                - globals.set:
                    id: ch5_energy_last
                    value: !lambda return id(ch5_energy_);
          # - lvgl.label.update:
          #     id: ch5_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']
    energy_6:
      id: ch6_energy
      name: "${ch6} energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: ch6_energy
                  above: 0.01
              then:
                globals.set:
                  id: ch6_energy_
                  value: !lambda return id(ch6_energy_last) + x;
              else:
                - globals.set:
                    id: ch6_energy_last
                    value: !lambda return id(ch6_energy_);
          - lvgl.label.update:
              id: ch6_energy_label
              text:
                format: "%.2f kWh"
                args: ['x']
    energy_sum:
      id: chs6_energy_sum
      name: "6-chs sum energy"
      #internal: true
      on_value:
        then:
          - if:
              condition:
                sensor.in_range:
                  id: chs6_energy_sum
                  above: 0.01
              then:
                globals.set:
                  id: chs6_energy_
                  value: !lambda return id(chs6_energy_last) + x;
              else:
                - globals.set:
                    id: chs6_energy_last
                    value: !lambda return id(chs6_energy_);
          # - lvgl.label.update:
          #     id: total_energy_label
          #     text:
          #       format: "%.2f kWh"
          #       args: ['x']

  - platform: template
    name: "${ch1} Total Energy"
    id: ch1_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch1_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: ch1_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']
  - platform: template
    name: "${ch2} Total Energy"
    id: ch2_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch2_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: ch2_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']
  - platform: template
    name: "${ch3} Total Energy"
    id: ch3_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch3_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: ch3_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']
  - platform: template
    name: "${ch4} Total Energy"
    id: ch4_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch4_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: ch4_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']
  - platform: template
    name: "${ch5} Total Energy"
    id: ch5_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch5_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: ch5_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']
  - platform: template
    name: "${ch6} Total Energy"
    id: ch6_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(ch6_energy_);
    update_interval: 10s
    # on_value:
    #   - lvgl.label.update:
    #       id: ch6_energy_label
    #       text:
    #         format: "%.2f kWh"
    #         args: ['x']
  - platform: template
    name: "6 ch Sum Total Energy"
    id: chs6_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    lambda: |-
      return id(chs6_energy_);
    update_interval: 10s
    on_value:
      - lvgl.label.update:
          id: total_energy_label
          text:
            format: "%.2f kWh"
            args: ['x']

button:
  - platform: template
    name: "读取校正值"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->refresh_all_calib_numbers();
  - platform: template
    name: "Update bl0906 Sensors"
    on_press:
      then:
        - logger.log: "正在读取bl0906传感器"
        - component.update: sensor_bl0906

number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH_1 Current Gain"
      id: chgn_1_adjust
      icon: "mdi:tune-vertical"
      mode: box

    chgn_decimal_2:
      name: "CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chgn_decimal_3:
      name: "CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chgn_decimal_4:
      name: "CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chgn_decimal_5:
      name: "CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chgn_decimal_6:
      name: "CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chgn_v_decimal:
      name: "Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box

    chos_decimal_1:
      name: "CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_decimal_2:
      name: "CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_decimal_3:
      name: "CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_decimal_4:
      name: "CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_decimal_5:
      name: "CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_decimal_6:
      name: "CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box

    chos_v_decimal:
      name: "Voltage Offset"
      icon: "mdi:tune"
      mode: box
    # 添加十进制形式的RMS校准值
    rmsgn_decimal_1:
      name: "CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsgn_decimal_2:
      name: "CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsgn_decimal_3:
      name: "CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsgn_decimal_4:
      name: "CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsgn_decimal_5:
      name: "CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsgn_decimal_6:
      name: "CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      
    rmsos_decimal_1:
      name: "CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      
    rmsos_decimal_2:
      name: "CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      
    rmsos_decimal_3:
      name: "CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      
    rmsos_decimal_4:
      name: "CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      
    rmsos_decimal_5:
      name: "CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      
    rmsos_decimal_6:
      name: "CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box


switch:
  - platform: template
    name: "bl0906 update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 update"
      - component.suspend: sensor_bl0906
    turn_on_action:
      - logger.log: "turn on bl0906 update"
      - component.resume: sensor_bl0906
