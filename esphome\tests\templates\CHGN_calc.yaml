defaults:
  phase_name: "A"
  phase_lower: "a"
sensor:
  - platform: template
    name: "${phase_name}_Current CH1 CHGN"
    #id: chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(ch${phase_name}1_current).state;
      float unit1 = id(unit1_current).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${phase_name}_Voltage CHGN"
    #id: voltage_chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(${phase_name}_voltage).state;
      float unit1 = id(unit1_voltage).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${phase_name}_Voltage Error"
    id: ${phase_lower}_voltage_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(${phase_name}_voltage).state - id(unit1_voltage).state) / id(unit1_voltage).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${phase_name}_Current Error"
    id: ${phase_lower}_current_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(ch${phase_name}1_current).state - id(unit1_current).state) / id(unit1_current).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate

button:
# 批量设置所有CHGN值
  - platform: template
    name: "批量更新所有${phase_name}相CHGN值"
    id: update_all_${phase_lower}_chgn_button
    on_press:
      - lambda: |-
          // 调用BL0906Factory的批量修改方法
          id(sensor_bl0906_${phase_lower})->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${phase_name}相CHGN 0"
    on_press:
      - lambda: id(sensor_bl0906_${phase_lower})->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
  # 批量清零RMSOS值
  - platform: template
    name: "${phase_name}相RMSOS 0"
    on_press:
      - lambda: id(sensor_bl0906_${phase_lower})->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate